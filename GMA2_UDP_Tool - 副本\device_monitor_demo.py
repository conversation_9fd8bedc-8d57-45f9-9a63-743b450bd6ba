#!/usr/bin/env python3
"""
设备监控功能演示程序
专门展示设备监控功能的独立版本
"""

import customtkinter as ctk
import threading
import time
import subprocess
import platform
import json
import os
from datetime import datetime
from tkinter import messagebox, simpledialog, filedialog

class DeviceMonitorApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("多媒体演出控制中心 - 设备监控系统")
        self.root.geometry("1200x800")
        
        # 设备监控相关变量
        self.monitored_devices = []
        self.device_groups = {"默认分组": []}
        self.monitoring_active = False
        self.device_logs = []
        
        self.create_widgets()
        self.load_devices()
        
        # 设置窗口关闭协议
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = ctk.CTkFrame(self.root)
        title_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(title_frame, text="🎭 多媒体演出控制中心 - 设备监控系统", 
                    font=("", 20, "bold")).pack(pady=10)
        
        # 控制面板
        control_frame = ctk.CTkFrame(self.root)
        control_frame.pack(fill="x", padx=10, pady=5)
        control_frame.grid_columnconfigure(3, weight=1)
        
        # 监控控制按钮
        self.start_btn = ctk.CTkButton(control_frame, text="🔍 开始监控", width=100,
                                      fg_color="#2E7D32", hover_color="#1B5E20",
                                      command=self.start_monitoring)
        self.start_btn.grid(row=0, column=0, padx=5, pady=5)
        
        self.stop_btn = ctk.CTkButton(control_frame, text="⏹ 停止监控", width=100,
                                     fg_color="#D32F2F", hover_color="#B71C1C",
                                     command=self.stop_monitoring)
        self.stop_btn.grid(row=0, column=1, padx=5, pady=5)
        
        self.refresh_btn = ctk.CTkButton(control_frame, text="🔄 刷新", width=80,
                                        fg_color="#FF9800", hover_color="#F57C00",
                                        command=self.refresh_devices)
        self.refresh_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # 状态显示
        status_frame = ctk.CTkFrame(control_frame)
        status_frame.grid(row=0, column=3, padx=20, pady=5, sticky="ew")
        
        self.status_label = ctk.CTkLabel(status_frame, text="监控已停止")
        self.status_label.pack(side="left", padx=10)
        
        self.count_label = ctk.CTkLabel(status_frame, text="设备: 0/0")
        self.count_label.pack(side="right", padx=10)
        
        # 设备管理按钮
        manage_frame = ctk.CTkFrame(control_frame)
        manage_frame.grid(row=0, column=4, padx=5, pady=5)
        
        ctk.CTkButton(manage_frame, text="添加设备", width=80,
                     command=self.add_device).pack(side="left", padx=2)
        ctk.CTkButton(manage_frame, text="创建分组", width=80,
                     command=self.create_group).pack(side="left", padx=2)
        ctk.CTkButton(manage_frame, text="查看日志", width=80,
                     fg_color="#9C27B0", hover_color="#7B1FA2",
                     command=self.show_logs).pack(side="left", padx=2)
        
        # 主要内容区域
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        main_frame.grid_columnconfigure(1, weight=1)
        main_frame.grid_rowconfigure(0, weight=1)
        
        # 左侧分组列表
        left_frame = ctk.CTkFrame(main_frame, width=300)
        left_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        left_frame.grid_rowconfigure(1, weight=1)
        
        ctk.CTkLabel(left_frame, text="设备分组", font=("", 16)).pack(pady=10)
        
        self.groups_frame = ctk.CTkScrollableFrame(left_frame, label_text="点击分组查看设备")
        self.groups_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 右侧设备状态
        right_frame = ctk.CTkFrame(main_frame)
        right_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)
        right_frame.grid_rowconfigure(1, weight=1)
        right_frame.grid_columnconfigure(0, weight=1)
        
        # 设备状态标题
        title_frame = ctk.CTkFrame(right_frame)
        title_frame.pack(fill="x", padx=10, pady=5)
        
        self.device_title = ctk.CTkLabel(title_frame, text="所有设备状态", font=("", 14))
        self.device_title.pack(side="left", padx=10, pady=5)
        
        # 筛选器
        filter_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        filter_frame.pack(side="right", padx=10, pady=5)
        
        ctk.CTkLabel(filter_frame, text="筛选:").pack(side="left", padx=5)
        self.filter_combo = ctk.CTkComboBox(filter_frame, values=["全部", "在线", "离线", "异常"], 
                                           width=100, command=self.update_display)
        self.filter_combo.pack(side="left", padx=5)
        self.filter_combo.set("全部")
        
        # 设备状态列表
        self.devices_frame = ctk.CTkScrollableFrame(right_frame, label_text="设备状态监控")
        self.devices_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
    def add_device(self):
        """添加设备对话框"""
        dialog = DeviceDialog(self.root, list(self.device_groups.keys()))
        if dialog.result:
            device = dialog.result
            device["id"] = len(self.monitored_devices) + 1
            device["status"] = "未知"
            device["last_check"] = None
            device["response_time"] = 0
            device["error_count"] = 0
            
            self.monitored_devices.append(device)
            self.save_devices()
            self.update_display()
            self.update_groups()
            print(f"添加设备: {device['name']} ({device['ip']})")
            
    def create_group(self):
        """创建设备分组"""
        name = simpledialog.askstring("创建分组", "请输入分组名称:", parent=self.root)
        if name and name not in self.device_groups:
            self.device_groups[name] = []
            self.save_devices()
            self.update_groups()
            print(f"创建分组: {name}")
            
    def start_monitoring(self):
        """开始监控"""
        if not self.monitored_devices:
            messagebox.showwarning("提示", "请先添加要监控的设备", parent=self.root)
            return
            
        self.monitoring_active = True
        self.start_btn.configure(state="disabled")
        self.stop_btn.configure(state="normal")
        self.status_label.configure(text="监控运行中...", text_color="green")
        
        self.check_devices()
        print("设备监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.status_label.configure(text="监控已停止", text_color="red")
        print("设备监控已停止")
        
    def refresh_devices(self):
        """手动刷新"""
        if not self.monitored_devices:
            messagebox.showinfo("提示", "没有要刷新的设备", parent=self.root)
            return
        print("手动刷新设备状态...")
        self.check_all_devices()
        
    def check_devices(self):
        """定期检查设备"""
        if self.monitoring_active:
            self.check_all_devices()
            self.root.after(30000, self.check_devices)  # 30秒后再次检查
            
    def check_all_devices(self):
        """检查所有设备"""
        for device in self.monitored_devices:
            threading.Thread(target=self.ping_device, args=(device,), daemon=True).start()
            
    def ping_device(self, device):
        """ping单个设备"""
        try:
            start_time = time.time()
            
            param = '-n' if platform.system().lower() == 'windows' else '-c'
            command = ['ping', param, '1', '-w', '3000', device['ip']]
            
            startupinfo = None
            if platform.system() == 'windows':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                
            result = subprocess.call(command, startupinfo=startupinfo,
                                   stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            response_time = (time.time() - start_time) * 1000
            
            old_status = device['status']
            if result == 0:
                device['status'] = '在线'
                device['response_time'] = response_time
                if old_status == '离线':
                    self.log_event(device, "设备恢复在线", "info")
            else:
                device['status'] = '离线'
                device['response_time'] = 0
                device['error_count'] += 1
                if old_status == '在线':
                    self.log_event(device, "设备离线", "error")
                    self.show_alert(device, "设备离线")
                    
            device['last_check'] = datetime.now().strftime('%H:%M:%S')
            self.root.after(0, self.update_display)
            
        except Exception as e:
            device['status'] = '异常'
            device['last_check'] = datetime.now().strftime('%H:%M:%S')
            device['error_count'] += 1
            self.log_event(device, f"检查异常: {e}", "error")
            self.root.after(0, self.update_display)
            
    def log_event(self, device, message, level="info"):
        """记录日志"""
        log = {
            "timestamp": datetime.now().isoformat(),
            "device_name": device["name"],
            "device_ip": device["ip"],
            "message": message,
            "level": level
        }
        self.device_logs.append(log)
        
        if len(self.device_logs) > 1000:
            self.device_logs = self.device_logs[-500:]
            
        print(f"[{level.upper()}] {device['name']}: {message}")
        
    def show_alert(self, device, message):
        """显示报警"""
        def alert():
            messagebox.showwarning("设备报警",
                                 f"设备: {device['name']} ({device['ip']})\n"
                                 f"状态: {message}\n"
                                 f"时间: {datetime.now().strftime('%H:%M:%S')}",
                                 parent=self.root)
        self.root.after(0, alert)
        
    def update_display(self, *args):
        """更新设备显示"""
        # 清空显示
        for widget in self.devices_frame.winfo_children():
            widget.destroy()
            
        # 统计
        online = sum(1 for d in self.monitored_devices if d['status'] == '在线')
        total = len(self.monitored_devices)
        self.count_label.configure(text=f"设备: {online}/{total}")
        
        # 筛选
        filter_status = self.filter_combo.get()
        
        for device in self.monitored_devices:
            if filter_status != "全部" and device['status'] != filter_status:
                continue
                
            # 设备卡片
            card = ctk.CTkFrame(self.devices_frame)
            card.pack(fill="x", padx=5, pady=3)
            
            # 基本信息
            info_frame = ctk.CTkFrame(card, fg_color="transparent")
            info_frame.pack(fill="x", padx=10, pady=5)
            
            ctk.CTkLabel(info_frame, text=device['name'], 
                        font=("", 14, "bold")).pack(side="left")
            ctk.CTkLabel(info_frame, text=f"({device['ip']})", 
                        text_color="gray").pack(side="left", padx=(10, 0))
            
            # 状态
            colors = {"在线": "green", "离线": "red", "异常": "orange", "未知": "gray"}
            ctk.CTkLabel(info_frame, text=f"● {device['status']}", 
                        text_color=colors.get(device['status'], "gray")).pack(side="right")
            
            # 详细信息
            detail_frame = ctk.CTkFrame(card, fg_color="transparent")
            detail_frame.pack(fill="x", padx=10, pady=(0, 5))
            
            if device['response_time'] > 0:
                resp_text = f"响应: {device['response_time']:.0f}ms"
            else:
                resp_text = "响应: --"
            ctk.CTkLabel(detail_frame, text=resp_text, text_color="gray").pack(side="left")
            
            if device['last_check']:
                check_text = f"检查: {device['last_check']}"
            else:
                check_text = "检查: 从未"
            ctk.CTkLabel(detail_frame, text=check_text, text_color="gray").pack(side="left", padx=(20, 0))
            
            if device['error_count'] > 0:
                ctk.CTkLabel(detail_frame, text=f"错误: {device['error_count']}", 
                            text_color="red").pack(side="right")
                            
    def update_groups(self):
        """更新分组显示"""
        for widget in self.groups_frame.winfo_children():
            widget.destroy()
            
        for group_name in self.device_groups:
            count = sum(1 for d in self.monitored_devices if d.get('group') == group_name)
            btn = ctk.CTkButton(self.groups_frame, text=f"📁 {group_name} ({count})",
                               command=lambda g=group_name: self.filter_by_group(g))
            btn.pack(fill="x", padx=5, pady=2)
            
    def filter_by_group(self, group_name):
        """按分组筛选"""
        print(f"筛选分组: {group_name}")
        # 这里可以实现分组筛选逻辑
        
    def show_logs(self):
        """显示日志窗口"""
        LogWindow(self.root, self.device_logs)
        
    def save_devices(self):
        """保存设备配置"""
        try:
            data = {
                "devices": self.monitored_devices,
                "groups": self.device_groups
            }
            with open("devices.json", 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存失败: {e}")
            
    def load_devices(self):
        """加载设备配置"""
        if os.path.exists("devices.json"):
            try:
                with open("devices.json", 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.monitored_devices = data.get("devices", [])
                self.device_groups = data.get("groups", {"默认分组": []})
                print(f"加载了 {len(self.monitored_devices)} 个设备")
            except Exception as e:
                print(f"加载失败: {e}")
                
        self.update_display()
        self.update_groups()
        
    def on_closing(self):
        """程序关闭"""
        self.monitoring_active = False
        self.save_devices()
        self.root.destroy()
        
    def run(self):
        """运行程序"""
        self.root.mainloop()

# 设备添加对话框
class DeviceDialog:
    def __init__(self, parent, groups):
        self.result = None
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("添加监控设备")
        self.dialog.geometry("400x350")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设备信息输入
        ctk.CTkLabel(self.dialog, text="设备名称:").pack(pady=5)
        self.name_entry = ctk.CTkEntry(self.dialog, width=300)
        self.name_entry.pack(pady=5)
        
        ctk.CTkLabel(self.dialog, text="IP地址:").pack(pady=5)
        self.ip_entry = ctk.CTkEntry(self.dialog, width=300)
        self.ip_entry.pack(pady=5)
        
        ctk.CTkLabel(self.dialog, text="设备类型:").pack(pady=5)
        self.type_combo = ctk.CTkComboBox(self.dialog, 
                                         values=["灯光控制器", "音响设备", "投影设备", "摄像设备", "网络设备", "其他"], 
                                         width=300)
        self.type_combo.pack(pady=5)
        self.type_combo.set("灯光控制器")
        
        ctk.CTkLabel(self.dialog, text="所属分组:").pack(pady=5)
        self.group_combo = ctk.CTkComboBox(self.dialog, values=groups, width=300)
        self.group_combo.pack(pady=5)
        if groups:
            self.group_combo.set(groups[0])
        
        ctk.CTkLabel(self.dialog, text="描述:").pack(pady=5)
        self.desc_entry = ctk.CTkEntry(self.dialog, width=300, placeholder_text="可选")
        self.desc_entry.pack(pady=5)
        
        # 按钮
        btn_frame = ctk.CTkFrame(self.dialog, fg_color="transparent")
        btn_frame.pack(pady=20)
        
        ctk.CTkButton(btn_frame, text="确定", command=self.ok_clicked).pack(side="left", padx=10)
        ctk.CTkButton(btn_frame, text="取消", command=self.cancel_clicked).pack(side="left", padx=10)
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        self.dialog.wait_window()

    def ok_clicked(self):
        name = self.name_entry.get().strip()
        ip = self.ip_entry.get().strip()
        
        if not name or not ip:
            messagebox.showwarning("输入错误", "请填写设备名称和IP地址", parent=self.dialog)
            return
        
        # IP验证
        try:
            parts = ip.split('.')
            if len(parts) != 4 or not all(0 <= int(part) <= 255 for part in parts):
                raise ValueError()
        except ValueError:
            messagebox.showwarning("输入错误", "请输入有效的IP地址", parent=self.dialog)
            return
        
        self.result = {
            "name": name,
            "ip": ip,
            "type": self.type_combo.get(),
            "group": self.group_combo.get(),
            "description": self.desc_entry.get().strip()
        }
        self.dialog.destroy()

    def cancel_clicked(self):
        self.dialog.destroy()

# 日志窗口
class LogWindow:
    def __init__(self, parent, logs):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("设备监控日志")
        self.window.geometry("800x600")
        self.window.transient(parent)
        
        # 日志显示
        self.log_text = ctk.CTkTextbox(self.window, font=("Courier New", 12))
        self.log_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.display_logs(logs)
        
        # 按钮
        btn_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        btn_frame.pack(pady=10)
        
        ctk.CTkButton(btn_frame, text="导出日志", command=lambda: self.export_logs(logs)).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="关闭", command=self.window.destroy).pack(side="left", padx=5)

    def display_logs(self, logs):
        self.log_text.delete("1.0", "end")
        
        if not logs:
            self.log_text.insert("1.0", "暂无日志记录")
            return
        
        recent_logs = logs[-100:] if len(logs) > 100 else logs
        
        for log in reversed(recent_logs):
            timestamp = datetime.fromisoformat(log["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
            symbols = {"info": "ℹ", "error": "❌", "warning": "⚠"}
            symbol = symbols.get(log["level"], "•")
            
            log_line = f"[{timestamp}] {symbol} {log['device_name']} ({log['device_ip']}): {log['message']}\n"
            self.log_text.insert("1.0", log_line)

    def export_logs(self, logs):
        if not logs:
            messagebox.showinfo("提示", "没有日志可导出", parent=self.window)
            return
        
        filename = filedialog.asksaveasfilename(
            parent=self.window,
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            title="导出日志"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("设备监控日志\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for log in logs:
                        timestamp = datetime.fromisoformat(log["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
                        f.write(f"[{timestamp}] [{log['level'].upper()}] {log['device_name']} ({log['device_ip']}): {log['message']}\n")
                
                messagebox.showinfo("成功", f"日志已导出到: {filename}", parent=self.window)
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}", parent=self.window)

def main():
    print("=" * 70)
    print("    多媒体演出控制中心 - 设备监控系统")
    print("=" * 70)
    print()
    print("🎭 专业级设备状态监控系统")
    print("✅ 实时设备状态检测")
    print("✅ 自动故障报警")
    print("✅ 设备分组管理")
    print("✅ 监控日志记录")
    print("✅ 响应时间测量")
    print()
    print("🚀 程序启动中...")
    
    app = DeviceMonitorApp()
    app.run()

if __name__ == "__main__":
    main()
