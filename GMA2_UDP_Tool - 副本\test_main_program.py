#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程序测试脚本
测试主程序的轨道编辑器功能
"""

import sys
import os
import json
import time
import threading

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_program():
    """测试主程序"""
    print("🚀 启动主程序测试...")
    
    try:
        # 导入主程序
        from ma2_msc_commander import App

        print("✅ 主程序模块导入成功")

        # 创建主程序实例
        app = App()
        
        print("✅ 主程序实例创建成功")
        
        # 检查是否有序列数据
        if hasattr(app, 'timeline_sequences') and app.timeline_sequences:
            print(f"✅ 发现 {len(app.timeline_sequences)} 个序列")

            # 设置当前序列
            app.current_sequence = app.timeline_sequences[0]
            print(f"✅ 设置当前序列: {app.current_sequence['name']}")
            
            # 等待界面完全加载
            def test_track_editor():
                time.sleep(2)  # 等待2秒
                try:
                    print("🎬 尝试打开轨道编辑器...")
                    
                    # 模拟点击轨道编辑器按钮
                    if hasattr(app, 'open_track_timeline_window'):
                        app.open_track_timeline_window()
                        print("✅ 轨道编辑器打开成功！")
                    else:
                        print("❌ 找不到轨道编辑器方法")
                        
                except Exception as e:
                    print(f"❌ 轨道编辑器测试失败: {e}")
            
            # 在后台线程中测试轨道编辑器
            test_thread = threading.Thread(target=test_track_editor)
            test_thread.daemon = True
            test_thread.start()
            
        else:
            print("⚠️ 没有找到序列数据")
        
        print("🎯 主程序启动完成，请手动测试轨道编辑器功能")

        # 运行主程序
        app.mainloop()
        
    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_program()
