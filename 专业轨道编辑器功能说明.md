# 🎬 专业轨道编辑器 - 类似剪映的专业功能

## 🚀 全新升级功能

### 核心特性
- **🎯 专业轨道编辑**：类似剪映/Premiere Pro的轨道界面
- **🖱️ 自由拖拽缩放**：片段可随意拖动调整时间
- **⏱️ 精确时间控制**：毫秒级精度的时间编辑
- **📺 实时预览窗口**：根据轨道内容实时显示预览
- **🎨 多媒体支持**：场景、视频、图片、音频片段

## 📋 详细功能说明

### 1. 专业工具栏

#### 播放控制区
```
▶️ 播放/暂停  ⏹️ 停止  ⏮️ 回到开始
时间显示: 00:00.000 (精确到毫秒)
```

#### 编辑工具区
```
✂️ 分割片段 - 在播放头位置分割当前片段
🗑️ 删除片段 - 删除选中的片段
📋 复制片段 - 复制到剪贴板
```

#### 缩放控制区
```
缩放滑块: 0.1x - 5.0x
适应按钮: 自动调整到最佳缩放比例
```

### 2. 轨道区域

#### 轨道标签（左侧）
- **视频轨道** - 主要视频内容
- **音频轨道** - 音频和音乐
- **灯光轨道1** - 主要灯光场景
- **灯光轨道2** - 辅助灯光效果
- **特效轨道** - 特殊效果
- **字幕轨道** - 文字和字幕

#### 轨道控制
每个轨道都有：
- **🔇 静音按钮** - 切换轨道静音
- **🔒 锁定按钮** - 锁定轨道防止误操作

### 3. 时间轴编辑区

#### 精确时间刻度
- **自适应刻度**：根据缩放级别自动调整
  - 高缩放：0.1秒间隔
  - 中缩放：1秒间隔  
  - 低缩放：5秒间隔
- **时间标签**：精确显示时:分:秒.毫秒

#### 片段操作
- **拖拽移动**：鼠标拖拽片段到任意时间位置
- **选择高亮**：选中片段显示金色边框
- **实时预览**：拖拽时实时更新时间线

### 4. 预览窗口（下方）

#### 智能预览
根据片段类型自动显示：

**场景预览**
```
🎭 场景标题显示
💡 模拟6个彩色灯光设备
🌈 光束效果可视化
```

**视频预览**
```
📹 视频播放框
▶️ 播放控制图标
📝 视频信息显示
```

**图片预览**
```
🖼️ 图片预览框
🏔️ 山峰图标示意
☀️ 太阳装饰元素
```

**默认预览**
```
🎬 通用媒体图标
📄 片段名称显示
```

## 🎯 操作指南

### 基础操作

#### 1. 选择片段
- **单击片段**：选择并高亮显示
- **选中效果**：金色边框 + 预览窗口更新

#### 2. 拖拽移动
- **拖拽操作**：按住鼠标左键拖动片段
- **实时反馈**：拖拽过程中实时更新位置
- **自动对齐**：释放时自动保存新位置

#### 3. 时间调整
- **播放头**：红色竖线显示当前时间
- **点击定位**：点击时间轴任意位置移动播放头
- **精确显示**：工具栏实时显示当前时间

### 高级操作

#### 1. 片段分割
```
步骤：
1. 选择要分割的片段
2. 移动播放头到分割位置
3. 点击 ✂️ 分割按钮
4. 片段自动分为两部分
```

#### 2. 复制粘贴
```
复制：选择片段 → 点击 📋 复制
粘贴：移动播放头 → Ctrl+V 或粘贴按钮
```

#### 3. 属性编辑
```
双击片段 → 打开属性对话框
可编辑：名称、时长、类型、颜色
```

#### 4. 右键菜单
```
右键片段显示菜单：
- 编辑属性
- 复制片段  
- 删除片段
- 取消操作
```

### 缩放和导航

#### 缩放控制
- **滑块缩放**：拖动缩放滑块 (0.1x - 5.0x)
- **鼠标滚轮**：Ctrl + 滚轮进行缩放
- **适应按钮**：一键调整到最佳缩放

#### 时间线导航
- **水平滚动**：鼠标滚轮左右滚动
- **滚动条**：使用底部滚动条导航
- **播放头跟随**：播放时自动滚动跟随

## ⌨️ 快捷键

### 播放控制
- **空格键** - 播放/暂停
- **Home键** - 回到开始

### 编辑操作
- **Delete** - 删除选中片段
- **Ctrl+C** - 复制片段
- **Ctrl+V** - 粘贴片段

### 导航缩放
- **Ctrl+滚轮** - 缩放时间轴
- **滚轮** - 水平滚动

## 🎨 视觉设计

### 现代化界面
- **深色主题**：专业的深色背景
- **高对比度**：清晰的文字和图标
- **渐变色彩**：美观的按钮和控件

### 轨道颜色方案
```
轨道1: #FF5722 (深橙色) - 视频轨道
轨道2: #2196F3 (蓝色)   - 音频轨道  
轨道3: #4CAF50 (绿色)   - 灯光轨道1
轨道4: #FF9800 (橙色)   - 灯光轨道2
轨道5: #9C27B0 (紫色)   - 特效轨道
轨道6: #607D8B (蓝灰)   - 字幕轨道
```

### 状态指示
- **选中状态**：金色边框 (#FFD700)
- **播放头**：红色线条 (#FF0000)
- **时间刻度**：白色/灰色渐变

## 🔧 技术特性

### 性能优化
- **增量渲染**：只更新变化的部分
- **虚拟滚动**：大量片段时的流畅滚动
- **缓存机制**：缓存计算结果提升性能

### 数据同步
- **实时保存**：操作后自动保存到序列
- **撤销支持**：支持操作历史记录
- **数据一致性**：与主程序数据实时同步

### 扩展性设计
- **插件接口**：支持自定义片段类型
- **主题系统**：可自定义颜色和样式
- **多语言**：界面文本可本地化

## 🚀 使用场景

### 专业演出制作
1. **导入音乐** → **自动节拍检测** → **智能片段分配**
2. **轨道编辑** → **精确时间调整** → **实时预览**
3. **效果预览** → **参数微调** → **最终确认**

### 多媒体内容编排
1. **视频片段** → **拖拽到视频轨道** → **调整时长**
2. **音频同步** → **对齐到音频轨道** → **精确匹配**
3. **特效添加** → **特效轨道编辑** → **预览效果**

### 教学演示
1. **概念展示** → **轨道结构教学** → **时间关系理解**
2. **操作演练** → **拖拽练习** → **编辑技巧**
3. **作品创作** → **独立编辑** → **成果展示**

## 💡 专业提示

### 最佳实践
1. **合理分轨**：不同类型内容放在对应轨道
2. **精确对齐**：使用播放头精确定位
3. **预览确认**：编辑后及时预览效果
4. **定期保存**：重要编辑后手动保存

### 效率技巧
1. **快捷键**：熟练使用键盘快捷键
2. **批量操作**：选择多个片段批量编辑
3. **模板复用**：保存常用编排作为模板
4. **预设管理**：创建常用设置预设

---

**专业轨道编辑器** - 让多媒体编排像剪映一样简单专业！🎬✨
