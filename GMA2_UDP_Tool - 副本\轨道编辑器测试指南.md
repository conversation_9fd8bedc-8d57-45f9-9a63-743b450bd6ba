# 轨道编辑器测试指南

## 快速测试步骤

### 1. 启动程序
```bash
cd "GMA2_UDP_Tool - 副本"
python ma2_msc_commander.py
```

### 2. 登录系统
- 用户名：`admin`
- 密码：`admin123`
- 点击"登录"

### 3. 打开轨道编辑器
1. 在主界面的"演出序列"区域
2. 选择一个序列（如"1212"或"嘀咕嘀咕"）
3. 点击"轨道编辑器"按钮
4. 轨道编辑器窗口应该正常打开

### 4. 验证基本功能

#### 4.1 界面检查
- ✅ 窗口标题显示"专业轨道编辑器 - 类似剪映"
- ✅ 顶部显示序列信息（序列名、片段数、总时长）
- ✅ 工具栏显示各种按钮（添加轨道、删除轨道、导入文件等）
- ✅ 左侧显示轨道标签（视频轨道、音频轨道等）
- ✅ 中间显示时间线画布
- ✅ 底部显示预览窗口和日志区域

#### 4.2 轨道显示检查
- ✅ 左侧应显示6个默认轨道：
  - 视频轨道
  - 音频轨道
  - 灯光轨道1
  - 灯光轨道2
  - 特效轨道
  - 字幕轨道
- ✅ 每个轨道都有静音和锁定按钮
- ✅ 时间线上应显示序列的片段

#### 4.3 时间线检查
- ✅ 顶部显示时间刻度尺
- ✅ 时间线上显示彩色的片段块
- ✅ 片段块显示场景名称
- ✅ 可以看到播放头（红色竖线）

### 5. 测试工具栏功能

#### 5.1 添加轨道功能
1. 点击"➕ 添加轨道"按钮
2. 在弹出的对话框中：
   - 输入轨道名称（如"测试轨道"）
   - 选择轨道类型
   - 选择轨道颜色
3. 点击"添加"
4. ✅ 新轨道应出现在轨道列表中

#### 5.2 播放控制功能
1. 点击"▶️"播放按钮
2. ✅ 按钮应变为"⏸️"暂停按钮
3. ✅ 时间显示应开始更新
4. 点击"⏹️"停止按钮
5. ✅ 播放应停止，时间重置

#### 5.3 缩放功能
1. 拖动缩放滑块
2. ✅ 时间线应相应缩放
3. 点击"适应"按钮
4. ✅ 时间线应适应窗口大小

#### 5.4 导入/导出功能
1. 点击"📁 导入"按钮
2. ✅ 应打开文件选择对话框
3. 点击"💾 导出"按钮
4. ✅ 应打开文件保存对话框

### 6. 测试预览功能

#### 6.1 外部预览
1. 点击"📺 外部显示"按钮
2. ✅ 应打开外部预览窗口
3. ✅ 预览窗口应显示当前内容

#### 6.2 全屏预览
1. 点击"🖥️ 全屏预览"按钮
2. ✅ 应进入全屏预览模式
3. 按ESC键退出全屏
4. ✅ 应正常退出全屏

### 7. 测试日志功能
1. 执行各种操作（添加轨道、播放等）
2. ✅ 底部日志区域应显示操作记录
3. ✅ 日志应包含时间戳和操作描述

## 常见问题排查

### 问题1：轨道编辑器无法打开
**症状**：点击"轨道编辑器"按钮没有反应或报错
**解决方案**：
1. 确保已选择一个序列
2. 检查控制台是否有错误信息
3. 重启程序重试

### 问题2：轨道显示为空
**症状**：轨道编辑器打开但没有显示任何片段
**解决方案**：
1. 确保选择的序列包含步骤
2. 检查序列数据是否正确
3. 尝试选择其他序列

### 问题3：添加轨道失败
**症状**：点击"添加轨道"后出现错误
**解决方案**：
1. 确保输入了轨道名称
2. 检查是否有权限问题
3. 查看日志区域的错误信息

### 问题4：预览窗口无内容
**症状**：预览窗口显示黑屏或无内容
**解决方案**：
1. 检查媒体文件路径是否正确
2. 确保VLC播放器已正确安装
3. 尝试播放其他媒体文件

## 性能测试

### 大序列测试
1. 创建包含多个步骤的长序列
2. 测试轨道编辑器的响应速度
3. 验证滚动和缩放的流畅性

### 多轨道测试
1. 添加多个自定义轨道
2. 测试轨道管理功能
3. 验证界面布局的适应性

## 报告问题

如果在测试过程中发现问题，请记录：
1. 具体的操作步骤
2. 出现的错误信息
3. 控制台输出
4. 系统环境信息

---
**测试版本**：修复版
**测试日期**：2025-06-15
**建议测试时间**：15-30分钟
