# 预设管理器功能说明

## 🎯 功能概述

预设管理器是一个强大的舞台灯光设计工具，允许用户保存、管理和快速应用各种预设配置，大大提高工作效率。

## 📋 主要功能

### 1. 场景预设
- **功能**：保存完整的舞台布局配置
- **包含内容**：
  - 灯杆位置和参数
  - 灯具配置和位置
  - 机柜布局
  - 备注信息
- **适用场景**：音乐会、戏剧、展览等不同类型的演出

### 2. 灯具组合预设
- **功能**：保存常用的灯具搭配方案
- **包含内容**：
  - 灯具类型和数量
  - DMX通道配置
  - 位置间距设置
- **使用方式**：可应用到任意现有灯杆上

### 3. 线缆模板
- **功能**：保存标准的走线方案
- **状态**：基础框架已完成，详细功能开发中
- **计划功能**：快速应用常用的线缆布局模板

### 4. 项目模板
- **功能**：保存完整的项目配置
- **包含内容**：所有项目设置和数据
- **用途**：作为新项目的起始模板

## 🎮 操作方式

### 访问预设管理器
1. 点击主界面的"预设管理器"按钮
2. 使用快捷键 `Ctrl+Shift+P`

### 创建预设
1. 在当前项目中完成布局设计
2. 打开预设管理器
3. 选择对应的预设类型标签页
4. 点击"创建XXX预设"按钮
5. 输入名称和描述

### 应用预设
1. 在预设列表中找到目标预设
2. 点击"应用"按钮
3. 确认应用操作

### 管理预设
- **编辑**：修改预设名称和描述
- **导出**：将预设保存为JSON文件
- **删除**：移除不需要的预设
- **导入**：从JSON文件导入预设

## ⌨️ 快捷键

- `Ctrl+Shift+P`：打开预设管理器
- `Ctrl+Shift+Q`：切换快速预设面板
- `Ctrl+Shift+1-9`：快速应用场景预设（按序号）

## 🔍 高级功能

### 搜索功能
- 每个预设类型都支持搜索
- 可按名称或描述搜索
- 实时过滤显示结果

### 批量操作
- 全部导出：将所有预设导出为JSON文件
- 清空所有：删除所有预设（需确认）
- 统计信息：显示各类预设的数量

### 快速预设面板
- 显示最常用的3个场景预设
- 一键快速应用
- 可通过快捷键切换显示

## 💾 数据存储

- 预设数据保存在浏览器本地存储中
- 支持导出/导入功能进行备份和分享
- 数据格式为标准JSON，便于交换

## 🎨 默认预设

系统提供以下默认预设：

### 场景预设
1. **音乐会基础布局**
   - 前台、后台、左右侧台灯杆
   - 基础摇头灯配置
   
2. **戏剧经典布局**
   - 台口、一道、二道、天幕灯杆
   - PAR灯配置

### 灯具组合预设
1. **摇头灯组合**：3台摇头灯标准配置
2. **染色灯阵列**：5台染色灯阵列配置

## 🔧 技术特性

- 响应式设计，适配不同屏幕尺寸
- 模块化架构，易于扩展
- 完整的错误处理和用户提示
- 支持数据验证和格式检查

## 📈 使用建议

1. **命名规范**：使用描述性的名称，便于识别
2. **分类管理**：按场景类型或用途分类保存
3. **定期备份**：导出重要预设作为备份
4. **团队协作**：通过导入/导出功能分享预设
5. **渐进使用**：从简单预设开始，逐步创建复杂配置

## 🚀 未来规划

1. 云端同步功能
2. 预设分享社区
3. 更多预设类型支持
4. 预设版本管理
5. 批量编辑功能
6. 预设预览功能

---

*预设管理器让舞台灯光设计更加高效和专业！*
