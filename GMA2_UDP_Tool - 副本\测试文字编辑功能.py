#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文字编辑功能
验证QuickTextEditorDialog是否正常工作
"""

import customtkinter as ctk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主程序中的QuickTextEditorDialog类
try:
    from ma2_msc_commander import QuickTextEditorDialog
    print("✅ 成功导入QuickTextEditorDialog类")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

class TestApp:
    def __init__(self):
        # 设置外观
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("文字编辑功能测试")
        self.root.geometry("400x300")
        
        self.create_interface()
        
    def create_interface(self):
        """创建测试界面"""
        # 标题
        title_label = ctk.CTkLabel(
            self.root, 
            text="文字编辑功能测试", 
            font=("", 20, "bold")
        )
        title_label.pack(pady=20)
        
        # 说明
        info_label = ctk.CTkLabel(
            self.root,
            text="点击下面的按钮测试文字编辑对话框",
            font=("", 12)
        )
        info_label.pack(pady=10)
        
        # 测试按钮
        test_btn = ctk.CTkButton(
            self.root,
            text="🖊️ 测试文字编辑",
            width=200,
            height=40,
            font=("", 14),
            command=self.test_text_editor
        )
        test_btn.pack(pady=20)
        
        # 结果显示区域
        self.result_frame = ctk.CTkFrame(self.root)
        self.result_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        self.result_label = ctk.CTkLabel(
            self.result_frame,
            text="等待测试...",
            font=("", 12)
        )
        self.result_label.pack(pady=20)
        
    def test_text_editor(self):
        """测试文字编辑器"""
        try:
            # 创建测试用的clip数据
            test_clip = {
                "text_data": {
                    "title": "测试标题",
                    "content": "这是测试文字内容\n可以编辑这些文字",
                    "duration": 15.0
                }
            }
            
            # 创建文字编辑对话框
            dialog = QuickTextEditorDialog(self.root, test_clip)
            
            # 等待对话框关闭
            self.root.wait_window(dialog.dialog)
            
            # 检查结果
            if hasattr(dialog, 'result') and dialog.result:
                result_text = f"""✅ 测试成功！
                
编辑结果：
标题: {dialog.result['title']}
时长: {dialog.result['duration']}秒
内容: {dialog.result['content'][:50]}{'...' if len(dialog.result['content']) > 50 else ''}
"""
                self.result_label.configure(text=result_text)
                print("✅ 文字编辑功能测试成功")
                print(f"结果: {dialog.result}")
            else:
                self.result_label.configure(text="❌ 测试取消或失败")
                print("❌ 用户取消了编辑")
                
        except Exception as e:
            error_text = f"❌ 测试失败: {str(e)}"
            self.result_label.configure(text=error_text)
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    def run(self):
        """运行测试应用"""
        print("🚀 启动文字编辑功能测试...")
        self.root.mainloop()

def main():
    """主函数"""
    print("=" * 50)
    print("文字编辑功能测试程序")
    print("=" * 50)
    
    try:
        app = TestApp()
        app.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
