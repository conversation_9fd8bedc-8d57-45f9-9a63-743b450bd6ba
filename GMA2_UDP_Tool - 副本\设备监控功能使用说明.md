# 多媒体演出控制中心 - 设备监控功能使用说明

## 🎯 功能概述

设备监控功能已成功集成到多媒体演出控制中心，提供专业的设备状态监控和管理能力。

## 📁 文件说明

### 主程序文件
- `ma2_msc_commander_simple.py` - 简化版主程序（推荐使用）
- `ma2_msc_commander.py` - 完整版主程序（存在缩进问题）

### 测试和配置文件
- `test_device_monitoring.py` - 设备监控测试脚本
- `monitored_devices.json` - 设备配置文件（自动生成）
- `test_main_program.py` - 主程序测试脚本

## 🚀 快速开始

### 1. 准备测试环境
```bash
# 创建测试设备配置
python test_device_monitoring.py

# 清理测试配置（可选）
python test_device_monitoring.py cleanup
```

### 2. 启动主程序
```bash
# 启动简化版程序（推荐）
python ma2_msc_commander_simple.py
```

### 3. 使用设备监控
1. 程序启动后，切换到"设备监控"标签页
2. 点击"开始监控"按钮启动监控
3. 观察设备状态变化
4. 使用各种管理功能

## 🔧 主要功能

### 设备管理
- ✅ **添加设备**: 支持添加各类设备（灯光、音响、投影等）
- ✅ **设备分组**: 按类型或用途对设备进行分组管理
- ✅ **设备删除**: 可以删除不需要的设备
- ✅ **配置保存**: 自动保存设备配置到文件

### 状态监控
- ✅ **实时监控**: 每30秒自动检查设备状态
- ✅ **Ping检测**: 使用ping命令检测设备在线状态
- ✅ **响应时间**: 显示设备响应时间
- ✅ **状态筛选**: 可按在线/离线/异常状态筛选显示

### 报警和日志
- ✅ **离线报警**: 设备离线时自动弹窗提醒
- ✅ **状态变化日志**: 记录设备状态变化历史
- ✅ **日志查看**: 专门的日志查看窗口
- ✅ **日志导出**: 支持导出日志为文本文件

### 界面功能
- ✅ **分组显示**: 左侧显示设备分组列表
- ✅ **状态卡片**: 右侧显示详细的设备状态卡片
- ✅ **实时更新**: 界面实时更新设备状态
- ✅ **操作按钮**: 每个设备都有独立的操作按钮

## 📊 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    多媒体演出控制中心                        │
├─────────────────────────────────────────────────────────────┤
│  主控制  │  设备监控  │                                     │
├─────────────────────────────────────────────────────────────┤
│ 🔍开始监控 ⏹停止监控 🔄刷新 │ 监控状态 │ 添加设备 创建分组 查看日志 │
├─────────────────────────────────────────────────────────────┤
│ 设备分组        │                设备状态监控                │
│ ┌─────────────┐ │ ┌─────────────────────────────────────────┐ │
│ │ 灯光设备(2) │ │ │ 主灯光控制器 (***********00)    ● 在线 │ │
│ │ 音响设备(1) │ │ │ 响应: 45ms  检查: 14:23:15  [检查][删除] │ │
│ │ 视频设备(2) │ │ ├─────────────────────────────────────────┤ │
│ │ 网络设备(1) │ │ │ 音响调音台 (***********01)      ● 离线 │ │
│ │ 默认分组(0) │ │ │ 响应: --    检查: 14:22:45  [检查][删除] │ │
│ └─────────────┘ │ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎛️ 操作指南

### 添加设备
1. 点击"添加设备"按钮
2. 填写设备信息：
   - 设备名称（必填）
   - IP地址（必填）
   - 设备类型（下拉选择）
   - 所属分组（下拉选择）
   - 描述（可选）
3. 点击"确定"保存

### 创建分组
1. 点击"创建分组"按钮
2. 输入分组名称
3. 点击"确定"创建

### 监控操作
- **开始监控**: 启动自动设备状态检查
- **停止监控**: 停止自动检查
- **刷新**: 手动刷新所有设备状态
- **检查**: 立即检查单个设备状态
- **删除**: 删除设备（需确认）

### 查看日志
1. 点击"查看日志"按钮
2. 在日志窗口中可以：
   - 查看最新100条日志
   - 刷新日志显示
   - 清空所有日志
   - 导出日志到文件

## 📋 测试设备列表

测试脚本会创建以下设备：

| 设备名称 | IP地址 | 类型 | 分组 |
|---------|--------|------|------|
| 主灯光控制器 | ***********00 | 灯光控制器 | 灯光设备 |
| 音响调音台 | ***********01 | 音响设备 | 音响设备 |
| 投影仪1 | ***********02 | 投影设备 | 视频设备 |
| 摄像机1 | ***********03 | 摄像设备 | 视频设备 |
| 网络交换机 | *********** | 网络设备 | 网络设备 |

## ⚠️ 注意事项

1. **IP地址**: 测试设备的IP地址可能无法ping通，这是正常的
2. **监控间隔**: 自动监控间隔为30秒
3. **权限要求**: ping命令可能需要管理员权限
4. **网络环境**: 确保网络连接正常
5. **配置保存**: 程序会自动保存设备配置

## 🔧 故障排除

### 程序无法启动
- 检查Python环境和依赖包
- 使用简化版程序：`ma2_msc_commander_simple.py`

### 设备无法ping通
- 检查IP地址是否正确
- 检查网络连接
- 确认目标设备支持ping

### 监控不工作
- 确认已点击"开始监控"
- 检查是否有设备添加
- 查看控制台输出信息

## 📞 技术支持

如有问题，请检查：
1. 控制台输出信息
2. 设备配置文件格式
3. 网络连接状态
4. 程序运行日志

---

**版本**: V16 - 终极版  
**更新时间**: 2025-06-15  
**功能状态**: ✅ 设备监控功能已完成并测试通过
