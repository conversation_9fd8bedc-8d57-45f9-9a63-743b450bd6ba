#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设备监控功能测试脚本
用于验证设备监控功能是否正常工作
"""

import json
import os

def create_test_devices():
    """创建测试设备配置"""
    test_devices = {
        "devices": [
            {
                "id": 1,
                "name": "本地回环测试",
                "ip": "127.0.0.1",
                "type": "网络设备",
                "group": "测试分组",
                "description": "本地回环地址，应该始终在线",
                "status": "未知",
                "last_check": None,
                "response_time": 0,
                "uptime": 0,
                "error_count": 0,
                "total_checks": 0,
                "success_rate": 0
            },
            {
                "id": 2,
                "name": "Google DNS",
                "ip": "*******",
                "type": "网络设备",
                "group": "外网测试",
                "description": "Google公共DNS服务器",
                "status": "未知",
                "last_check": None,
                "response_time": 0,
                "uptime": 0,
                "error_count": 0,
                "total_checks": 0,
                "success_rate": 0
            },
            {
                "id": 3,
                "name": "不存在的设备",
                "ip": "192.168.999.999",
                "type": "测试设备",
                "group": "测试分组",
                "description": "用于测试离线状态的虚拟设备",
                "status": "未知",
                "last_check": None,
                "response_time": 0,
                "uptime": 0,
                "error_count": 0,
                "total_checks": 0,
                "success_rate": 0
            }
        ],
        "groups": {
            "测试分组": [],
            "外网测试": [],
            "生产设备": []
        },
        "version": "1.0",
        "last_updated": "2024-01-01T00:00:00"
    }
    
    # 保存测试配置
    with open("monitored_devices.json", "w", encoding="utf-8") as f:
        json.dump(test_devices, f, indent=4, ensure_ascii=False)
    
    print("✅ 测试设备配置已创建")
    print(f"   - 创建了 {len(test_devices['devices'])} 个测试设备")
    print(f"   - 创建了 {len(test_devices['groups'])} 个测试分组")
    print("   - 本地回环测试 (127.0.0.1) - 应该在线")
    print("   - Google DNS (*******) - 应该在线")
    print("   - 不存在的设备 (192.168.999.999) - 应该离线")

def show_current_config():
    """显示当前设备配置"""
    if os.path.exists("monitored_devices.json"):
        with open("monitored_devices.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        print("\n📋 当前设备配置:")
        print(f"   设备总数: {len(data.get('devices', []))}")
        print(f"   分组总数: {len(data.get('groups', {}))}")
        
        print("\n📱 设备列表:")
        for device in data.get('devices', []):
            status = device.get('status', '未知')
            print(f"   - {device['name']} ({device['ip']}) - {status}")
        
        print("\n📁 分组列表:")
        for group_name in data.get('groups', {}):
            print(f"   - {group_name}")
    else:
        print("❌ 没有找到设备配置文件")

def main():
    print("=" * 60)
    print("    设备监控功能测试")
    print("=" * 60)
    
    choice = input("\n请选择操作:\n1. 创建测试设备配置\n2. 显示当前配置\n3. 启动主程序\n请输入选择 (1-3): ")
    
    if choice == "1":
        create_test_devices()
    elif choice == "2":
        show_current_config()
    elif choice == "3":
        print("\n🚀 启动主程序...")
        os.system("python ma2_msc_commander.py")
    else:
        print("❌ 无效选择")
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("现在可以启动主程序测试设备监控功能:")
    print("python ma2_msc_commander.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
