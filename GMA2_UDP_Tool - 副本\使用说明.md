# 多媒体演出控制中心 - 完整版使用说明

## 🎯 程序概述

**多媒体演出控制中心 V16 完整版** 是一个集成了所有功能的专业演出控制软件，包含：

- ✅ **主控制功能** - UDP命令发送、场景管理
- ✅ **设备监控功能** - 实时设备状态监控、日志记录
- ✅ **演出序列功能** - 时间轴编排、自动播放
- ✅ **定时任务功能** - 场景定时执行

## 📁 文件说明

### 主程序文件
- `ma2_msc_commander_fixed.py` - **完整版主程序**（推荐使用）
- `ma2_msc_commander_simple.py` - 简化版程序（仅设备监控）
- `ma2_msc_commander.py` - 原版程序（有缩进问题）

### 数据文件
- `scenes.json` - 场景配置文件
- `sequences.json` - 演出序列配置文件
- `monitored_devices.json` - 设备监控配置文件
- `vlc_path.txt` - VLC路径配置文件（可选）

## 🚀 启动程序

```bash
python ma2_msc_commander_fixed.py
```

## 📋 功能使用指南

### 1. 主控制功能

#### 网络设置
- **目标IP**: 设置灯光控制台的IP地址（默认：*************）
- **端口**: 设置UDP端口（默认：8000）
- **设备ID**: 设置MSC设备ID（默认：1）

#### 命令发送
1. 选择命令格式（General Light/Moving Light/All）
2. 选择命令类型（Go/Stop/Resume/Timed_Go/Set/Fire/Go_Off）
3. 根据命令类型填写相应参数
4. 点击"发送命令"执行

#### 场景管理
- **保存场景**: 将当前设置保存为新场景
- **加载场景**: 点击场景名称加载设置
- **运行场景**: 点击场景右侧的"▶"按钮或双击场景名称
- **更新场景**: 修改设置后点击"更新选中"
- **删除场景**: 选中场景后点击"删除"

### 2. 设备监控功能

#### 添加设备
1. 切换到"设备监控"标签页
2. 点击"添加设备"按钮
3. 填写设备信息：
   - 设备名称
   - IP地址
   - 设备类型
   - 所属分组
   - 描述（可选）

#### 开始监控
1. 添加要监控的设备
2. 点击"🔍 开始监控"
3. 系统将每30秒检查一次设备状态
4. 设备状态变化时会显示报警

#### 设备分组
- 点击"创建分组"创建设备分组
- 在添加设备时选择分组
- 点击分组名称查看该分组的设备

#### 查看日志
- 点击"查看日志"查看设备监控日志
- 支持日志导出功能
- 可清空历史日志

### 3. 演出序列功能

#### 创建序列
1. 切换到"演出序列"标签页
2. 点击"新建序列"
3. 输入序列名称

#### 添加场景到序列
1. 在左侧选择一个场景
2. 选择一个演出序列
3. 点击"添加场景"
4. 设置延迟时间（秒）

#### 播放序列
1. 选择要播放的序列
2. 点击"▶ 播放"开始播放
3. 可以使用"⏸ 暂停"和"⏹ 停止"控制播放

#### 编辑序列
- 在时间轴中可以编辑每个步骤的延迟时间
- 可以删除不需要的步骤
- 序列会自动保存

### 4. 定时任务功能

#### 设置定时任务
1. 在左侧选择一个场景
2. 点击"为选中场景设置定时"
3. 设置执行时间（小时:分钟）
4. 选择重复类型：
   - 仅一次：只执行一次
   - 每天：每天在指定时间执行
   - 每周：每周执行（开发中）
   - 每月：每月执行（开发中）

#### 管理定时任务
- 在"定时任务列表"中查看所有任务
- 点击"删除"按钮移除不需要的任务
- 任务会显示下次执行时间

## ⚙️ 高级设置

### VLC媒体播放
- 如果系统检测不到VLC，会显示"媒体播放不可用"
- 点击"手动设置VLC路径"选择VLC安装目录
- 重启程序后生效

### 网络扫描
- 需要安装scapy库才能使用网络扫描功能
- 安装命令：`pip install scapy`

### 数据备份
- 定期备份以下文件：
  - `scenes.json` - 场景数据
  - `sequences.json` - 序列数据
  - `monitored_devices.json` - 设备配置

## 🔧 故障排除

### 程序无法启动
1. 检查Python版本（建议3.8+）
2. 安装依赖：`pip install customtkinter apscheduler`
3. 检查文件权限

### 设备监控不工作
1. 确保设备IP地址正确
2. 检查网络连接
3. 确认防火墙设置

### UDP命令发送失败
1. 检查目标IP和端口
2. 确认灯光控制台网络设置
3. 检查防火墙是否阻止UDP通信

### VLC播放问题
1. 确保VLC已正确安装
2. 手动设置VLC路径
3. 检查媒体文件格式

## 📞 技术支持

如遇到问题，请检查：
1. 控制台输出的错误信息
2. 网络连接状态
3. 文件权限设置
4. 依赖库安装情况

---

**版本**: V16 完整版  
**更新日期**: 2024年  
**状态**: 所有功能已集成并测试通过 ✅
