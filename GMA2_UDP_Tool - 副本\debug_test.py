#!/usr/bin/env python3
"""
调试测试脚本 - 检查依赖和基本功能
"""

import sys
import traceback

def check_dependencies():
    """检查所有依赖库"""
    dependencies = {
        'socket': 'socket',
        'threading': 'threading', 
        'customtkinter': 'customtkinter',
        'json': 'json',
        'os': 'os',
        'platform': 'platform',
        'subprocess': 'subprocess',
        'time': 'time',
        'tkinter': 'tkinter',
        'datetime': 'datetime',
        'apscheduler': 'apscheduler.schedulers.background'
    }
    
    print("检查依赖库...")
    missing = []
    
    for name, module in dependencies.items():
        try:
            __import__(module)
            print(f"✓ {name}")
        except ImportError as e:
            print(f"✗ {name}: {e}")
            missing.append(name)
    
    return missing

def check_optional_dependencies():
    """检查可选依赖"""
    print("\n检查可选依赖...")
    
    # VLC
    try:
        import vlc
        print("✓ VLC 可用")
    except ImportError:
        print("⚠ VLC 不可用 (可选)")
    
    # Scapy
    try:
        from scapy.all import srp, Ether, ARP
        print("✓ Scapy 可用")
    except ImportError:
        print("⚠ Scapy 不可用 (可选)")

def test_basic_gui():
    """测试基本GUI创建"""
    print("\n测试基本GUI...")
    try:
        import customtkinter as ctk
        
        # 创建简单窗口
        root = ctk.CTk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        label = ctk.CTkLabel(root, text="如果您看到这个窗口，说明GUI正常工作")
        label.pack(pady=20)
        
        button = ctk.CTkButton(root, text="关闭", command=root.destroy)
        button.pack(pady=10)
        
        print("✓ GUI窗口创建成功")
        print("请检查是否有窗口弹出...")
        
        # 设置3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
    except Exception as e:
        print(f"✗ GUI创建失败: {e}")
        traceback.print_exc()
        return False

def test_main_app():
    """测试主应用程序"""
    print("\n测试主应用程序...")
    try:
        import ma2_msc_commander
        
        print("正在创建应用程序...")
        app = ma2_msc_commander.App()
        
        print("✓ 应用程序创建成功")
        print("请检查是否有主窗口显示...")
        
        # 设置5秒后自动关闭
        app.after(5000, app.destroy)
        app.mainloop()
        
        return True
    except Exception as e:
        print(f"✗ 主应用程序启动失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== ma2_msc_commander.py 调试测试 ===\n")
    
    # 检查依赖
    missing = check_dependencies()
    if missing:
        print(f"\n❌ 缺少必要依赖: {', '.join(missing)}")
        print("请安装缺少的库后重试")
        return
    
    check_optional_dependencies()
    
    # 测试基本GUI
    if not test_basic_gui():
        print("\n❌ 基本GUI测试失败，请检查customtkinter安装")
        return
    
    # 测试主应用
    if test_main_app():
        print("\n🎉 所有测试通过！应用程序应该可以正常运行")
    else:
        print("\n❌ 主应用程序测试失败")

if __name__ == "__main__":
    main()
