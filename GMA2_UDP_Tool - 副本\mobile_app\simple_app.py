#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多媒体演出控制中心 - 简化版移动端APP
使用tkinter构建，可以直接在Windows平板上运行
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import requests
import json
import threading
import time
from datetime import datetime

class PerformanceControlApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎭 演出控制中心")
        self.root.geometry("400x700")
        
        # 设置窗口图标和样式
        self.setup_window()
        
        # 服务器配置
        self.server_url = ""
        self.connected = False
        self.scenes = []
        
        # 创建界面
        self.create_interface()
        
        # 启动状态更新线程
        self.start_status_thread()
    
    def setup_window(self):
        """设置窗口样式"""
        # 设置窗口始终在最前
        self.root.attributes('-topmost', True)
        
        # 配置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TButton', background='#4CAF50')
        style.configure('Danger.TButton', background='#F44336')
        style.configure('Primary.TButton', background='#2196F3')
    
    def create_interface(self):
        """创建主界面"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎭 演出控制中心", style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 连接区域
        self.create_connection_section(main_frame, 1)
        
        # 状态区域
        self.create_status_section(main_frame, 2)
        
        # 控制区域
        self.create_control_section(main_frame, 3)
        
        # 场景区域
        self.create_scene_section(main_frame, 4)
    
    def create_connection_section(self, parent, row):
        """创建连接区域"""
        conn_frame = ttk.LabelFrame(parent, text="🔗 服务器连接", padding="10")
        conn_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        conn_frame.columnconfigure(1, weight=1)
        
        # 服务器地址
        ttk.Label(conn_frame, text="地址:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.server_entry = ttk.Entry(conn_frame, width=30)
        self.server_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        self.server_entry.insert(0, "192.168.1.100:8080")
        
        # 连接按钮
        self.connect_btn = ttk.Button(conn_frame, text="连接", command=self.connect_server)
        self.connect_btn.grid(row=0, column=2)
        
        # 连接状态
        self.status_label = ttk.Label(conn_frame, text="🔴 未连接", foreground="red")
        self.status_label.grid(row=1, column=0, columnspan=3, pady=(5, 0))
    
    def create_status_section(self, parent, row):
        """创建状态区域"""
        status_frame = ttk.LabelFrame(parent, text="📊 系统状态", padding="10")
        status_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_text = tk.Text(status_frame, height=4, width=40, state='disabled')
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 滚动条
        status_scroll = ttk.Scrollbar(status_frame, orient="vertical", command=self.status_text.yview)
        status_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=status_scroll.set)
    
    def create_control_section(self, parent, row):
        """创建控制区域"""
        control_frame = ttk.LabelFrame(parent, text="🎮 快速控制", padding="10")
        control_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        
        # 停止按钮
        self.stop_btn = ttk.Button(control_frame, text="⏹ 停止场景", 
                                  style='Danger.TButton', command=self.stop_scene)
        self.stop_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 唤醒按钮
        self.wake_btn = ttk.Button(control_frame, text="🔌 唤醒设备", 
                                  style='Success.TButton', command=self.wake_devices)
        self.wake_btn.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(control_frame, text="🔄 刷新数据", command=self.refresh_data)
        self.refresh_btn.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
    
    def create_scene_section(self, parent, row):
        """创建场景区域"""
        scene_frame = ttk.LabelFrame(parent, text="🎬 场景控制", padding="10")
        scene_frame.grid(row=row, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        scene_frame.columnconfigure(0, weight=1)
        parent.rowconfigure(row, weight=1)
        
        # 场景列表
        self.scene_listbox = tk.Listbox(scene_frame, height=8)
        self.scene_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        # 滚动条
        scene_scroll = ttk.Scrollbar(scene_frame, orient="vertical", command=self.scene_listbox.yview)
        scene_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.scene_listbox.configure(yscrollcommand=scene_scroll.set)
        
        # 播放按钮
        self.play_btn = ttk.Button(scene_frame, text="▶ 播放选中场景", 
                                  style='Primary.TButton', command=self.play_selected_scene)
        self.play_btn.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))
    
    def connect_server(self):
        """连接服务器"""
        server_addr = self.server_entry.get().strip()
        if not server_addr:
            messagebox.showerror("错误", "请输入服务器地址")
            return
        
        if not server_addr.startswith('http'):
            server_addr = 'http://' + server_addr
        
        self.server_url = server_addr
        
        # 在后台线程中测试连接
        threading.Thread(target=self.test_connection, daemon=True).start()
    
    def test_connection(self):
        """测试服务器连接"""
        try:
            response = requests.get(f"{self.server_url}/api/status", timeout=5)
            if response.status_code == 200:
                self.connected = True
                self.root.after(0, self.connection_success)
            else:
                self.root.after(0, lambda: self.connection_failed("服务器响应错误"))
        except Exception as e:
            error_msg = f"连接失败: {str(e)}"
            self.root.after(0, lambda: self.connection_failed(error_msg))
    
    def connection_success(self):
        """连接成功"""
        self.status_label.config(text="🟢 已连接", foreground="green")
        self.connect_btn.config(text="断开", command=self.disconnect_server)
        self.refresh_data()
        messagebox.showinfo("成功", "服务器连接成功！")
    
    def connection_failed(self, error_msg):
        """连接失败"""
        self.status_label.config(text="🔴 连接失败", foreground="red")
        messagebox.showerror("连接失败", error_msg)
    
    def disconnect_server(self):
        """断开服务器连接"""
        self.connected = False
        self.server_url = ""
        self.status_label.config(text="🔴 未连接", foreground="red")
        self.connect_btn.config(text="连接", command=self.connect_server)
        self.scene_listbox.delete(0, tk.END)
        self.update_status_display("已断开连接")
    
    def refresh_data(self):
        """刷新数据"""
        if not self.connected:
            return
        
        threading.Thread(target=self.load_scenes, daemon=True).start()
        threading.Thread(target=self.load_status, daemon=True).start()
    
    def load_scenes(self):
        """加载场景列表"""
        try:
            response = requests.get(f"{self.server_url}/api/scenes", timeout=5)
            if response.status_code == 200:
                self.scenes = response.json()
                self.root.after(0, self.update_scene_list)
        except Exception as e:
            print(f"加载场景失败: {e}")
    
    def update_scene_list(self):
        """更新场景列表"""
        self.scene_listbox.delete(0, tk.END)
        for scene in self.scenes:
            scene_name = scene.get('name', '未知场景')
            self.scene_listbox.insert(tk.END, scene_name)
    
    def load_status(self):
        """加载系统状态"""
        try:
            response = requests.get(f"{self.server_url}/api/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                status_text = f"""当前用户: {status_data.get('current_user', '未知')}
用户角色: {status_data.get('user_role', '未知')}
场景数量: {status_data.get('scenes_count', 0)}
监控设备: {status_data.get('devices_count', 0)}
唤醒设备: {status_data.get('wol_devices_count', 0)}
更新时间: {datetime.now().strftime('%H:%M:%S')}"""
                self.root.after(0, lambda: self.update_status_display(status_text))
        except Exception as e:
            print(f"加载状态失败: {e}")
    
    def update_status_display(self, text):
        """更新状态显示"""
        self.status_text.config(state='normal')
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(1.0, text)
        self.status_text.config(state='disabled')
    
    def play_selected_scene(self):
        """播放选中的场景"""
        selection = self.scene_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个场景")
            return
        
        scene_index = selection[0]
        if scene_index < len(self.scenes):
            scene_name = self.scenes[scene_index]['name']
            threading.Thread(target=self.play_scene, args=(scene_name,), daemon=True).start()
    
    def play_scene(self, scene_name):
        """播放场景"""
        try:
            response = requests.get(f"{self.server_url}/api/play?scene={scene_name}", timeout=5)
            if response.status_code == 200:
                result = response.json()
                self.root.after(0, lambda: messagebox.showinfo("成功", result.get('message', '场景开始播放')))
            else:
                self.root.after(0, lambda: messagebox.showerror("失败", "播放场景失败"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"播放失败: {str(e)}"))
    
    def stop_scene(self):
        """停止场景"""
        threading.Thread(target=self._stop_scene, daemon=True).start()
    
    def _stop_scene(self):
        """停止场景（后台）"""
        try:
            response = requests.get(f"{self.server_url}/api/stop", timeout=5)
            if response.status_code == 200:
                result = response.json()
                self.root.after(0, lambda: messagebox.showinfo("成功", result.get('message', '场景已停止')))
            else:
                self.root.after(0, lambda: messagebox.showerror("失败", "停止场景失败"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"停止失败: {str(e)}"))
    
    def wake_devices(self):
        """唤醒设备"""
        threading.Thread(target=self._wake_devices, daemon=True).start()
    
    def _wake_devices(self):
        """唤醒设备（后台）"""
        try:
            response = requests.get(f"{self.server_url}/api/wake_all", timeout=5)
            if response.status_code == 200:
                result = response.json()
                self.root.after(0, lambda: messagebox.showinfo("成功", result.get('message', '设备唤醒信号已发送')))
            else:
                self.root.after(0, lambda: messagebox.showerror("失败", "唤醒设备失败"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"唤醒失败: {str(e)}"))
    
    def start_status_thread(self):
        """启动状态更新线程"""
        def update_loop():
            while True:
                if self.connected:
                    self.load_status()
                time.sleep(10)  # 每10秒更新一次
        
        threading.Thread(target=update_loop, daemon=True).start()
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    """主函数"""
    app = PerformanceControlApp()
    app.run()

if __name__ == '__main__':
    main()
