# 🎬 专业轨道编辑器 - 快速测试指南

## 🚀 测试准备

### 1. 确认程序状态
✅ **程序已启动**：看到pygame和VLC加载成功
✅ **登录完成**：管理员权限登录
✅ **序列选择**：选择了"嘀咕嘀咕"序列

### 2. 媒体文件确认
✅ **音频文件**：`music/青花瓷.mp3`
✅ **视频文件**：`Video/钢铁侠.mp4`
✅ **场景数据**：scenes.json包含媒体路径

## 📋 核心功能测试

### 测试1：打开轨道编辑器
```
步骤：
1. 在时间轴编辑器中点击 "🎬 轨道视图" 按钮
2. 观察新的专业界面是否正确显示

预期结果：
✅ 显示专业工具栏（播放控制、编辑工具、专业工具、缩放控制）
✅ 显示6个彩色轨道标签（视频、音频、灯光等）
✅ 显示轨道编辑区域和预览窗口
✅ 片段正确分布在不同轨道上
```

### 测试2：真实预览功能
```
步骤：
1. 点击任意一个片段（如"打开面光"）
2. 观察预览窗口的显示内容
3. 点击预览区域的 ▶️ 播放按钮

预期结果：
✅ 预览窗口显示：🎭 场景: 打开面光
✅ 显示：🎵 音频: 青花瓷.mp3
✅ 显示：🎬 视频: 钢铁侠.mp4
✅ 显示场景备注内容
✅ 点击播放后能听到音乐声音
✅ 预览窗口显示视频画面
```

### 测试3：拖拽和吸附功能
```
步骤：
1. 选择一个片段（观察金色边框）
2. 拖拽片段到不同时间位置
3. 观察吸附效果
4. 释放鼠标确认位置更新

预期结果：
✅ 片段显示金色选中边框
✅ 拖拽时片段跟随鼠标移动
✅ 接近网格线时自动吸附
✅ 接近其他片段边界时自动吸附
✅ 释放后位置正确保存
```

### 测试4：专业工具功能
```
步骤：
1. 点击 "🧲 吸附" 按钮测试开关
2. 点击 "⏰ 跳转" 按钮打开时间跳转对话框
3. 在对话框中输入时间并跳转
4. 点击 "📏 标尺" 按钮切换标尺模式

预期结果：
✅ 吸附按钮显示开启/关闭状态
✅ 时间跳转对话框正确显示当前时间
✅ 输入时间后播放头正确跳转
✅ 标尺模式显示更精确的时间刻度
```

### 测试5：缩放和导航
```
步骤：
1. 拖动缩放滑块到不同位置
2. 使用 Ctrl+滚轮缩放
3. 点击"适应"按钮
4. 使用滚轮水平滚动

预期结果：
✅ 滑块缩放时间轴正确响应
✅ 滚轮缩放流畅无延迟
✅ 适应按钮调整到最佳缩放
✅ 水平滚动正常工作
✅ 时间刻度根据缩放自动调整精度
```

### 测试6：片段编辑功能
```
步骤：
1. 双击任意片段打开属性编辑
2. 修改片段名称和时长
3. 右键点击片段查看菜单
4. 使用 ✂️ 分割功能分割片段

预期结果：
✅ 属性对话框正确显示片段信息
✅ 修改后片段属性正确更新
✅ 右键菜单显示完整操作选项
✅ 分割功能正确将片段分为两部分
```

## 🎯 高级功能测试

### 测试7：媒体播放控制
```
步骤：
1. 选择包含音频的片段
2. 点击预览播放按钮
3. 调整音量滑块
4. 点击停止按钮

预期结果：
✅ 音频文件开始播放
✅ 音量滑块正确控制音量
✅ 停止按钮正确停止播放
✅ 播放状态正确显示
```

### 测试8：时间轴播放
```
步骤：
1. 点击工具栏的 ▶️ 播放按钮
2. 观察播放头移动
3. 观察预览窗口跟随更新
4. 使用空格键暂停/播放

预期结果：
✅ 播放头红线正确移动
✅ 时间显示实时更新（毫秒精度）
✅ 预览窗口根据播放头位置更新内容
✅ 空格键快捷键正确响应
```

### 测试9：快捷键功能
```
步骤：
1. 选择片段后按 Delete 键
2. 使用 Ctrl+C 复制片段
3. 使用 Ctrl+V 粘贴片段
4. 使用 Ctrl+滚轮缩放

预期结果：
✅ Delete键正确删除选中片段
✅ 复制功能正确工作
✅ 粘贴在播放头位置创建新片段
✅ Ctrl+滚轮缩放响应正确
```

## 🔍 问题排查

### 常见问题及解决方案

#### 1. 音频无法播放
```
可能原因：
- pygame未正确初始化
- 音频文件路径错误
- 音频格式不支持

解决方案：
- 检查控制台是否有pygame加载信息
- 确认音频文件存在于指定路径
- 尝试使用MP3或WAV格式
```

#### 2. 视频无法显示
```
可能原因：
- VLC未正确安装
- 视频文件路径错误
- 视频编解码器问题

解决方案：
- 确认VLC auto-detection successful消息
- 检查视频文件是否存在
- 尝试使用MP4格式视频
```

#### 3. 拖拽不流畅
```
可能原因：
- 系统性能不足
- 缩放级别过高
- 片段数量过多

解决方案：
- 降低缩放级别
- 减少同时显示的片段数量
- 关闭其他占用资源的程序
```

#### 4. 预览窗口空白
```
可能原因：
- 场景数据未正确加载
- 媒体文件路径错误
- 预览画布未正确初始化

解决方案：
- 重新选择片段
- 检查scenes.json文件内容
- 重启轨道编辑器窗口
```

## 📊 性能基准

### 推荐配置
- **CPU**：Intel i5 或 AMD Ryzen 5 以上
- **内存**：8GB RAM 以上
- **显卡**：支持硬件加速的独立显卡
- **存储**：SSD硬盘（提升媒体加载速度）

### 性能指标
- **拖拽响应时间**：< 50ms
- **缩放操作延迟**：< 100ms
- **媒体播放启动**：< 2s
- **界面刷新率**：> 30fps

## 🎊 测试完成标准

### 基础功能 ✅
- [ ] 轨道编辑器正确打开
- [ ] 预览窗口显示真实场景信息
- [ ] 拖拽功能流畅工作
- [ ] 吸附功能正确响应

### 媒体播放 ✅
- [ ] 音频文件正确播放
- [ ] 视频文件正确显示
- [ ] 播放控制按钮正常工作
- [ ] 音量控制正确响应

### 专业功能 ✅
- [ ] 时间跳转精确工作
- [ ] 缩放功能流畅响应
- [ ] 快捷键正确响应
- [ ] 片段编辑功能完整

### 用户体验 ✅
- [ ] 界面美观专业
- [ ] 操作逻辑直观
- [ ] 错误处理友好
- [ ] 性能表现良好

---

**测试完成后，你将拥有一个真正专业级的轨道编辑器！** 🎬✨

**就像剪映一样简单，但专为演出控制而设计！** 🚀
