#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
远程控制功能测试脚本
"""

import requests
import json
import time

def test_remote_control():
    """测试远程控制功能"""
    server_url = "http://192.168.1.3:8080"
    
    print("🧪 远程控制功能测试")
    print("=" * 40)
    
    # 测试1: 获取系统状态
    print("\n1. 测试系统状态获取...")
    try:
        response = requests.get(f"{server_url}/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print("✅ 系统状态获取成功:")
            print(f"   当前用户: {status.get('current_user', '未知')}")
            print(f"   用户角色: {status.get('user_role', '未知')}")
            print(f"   场景数量: {status.get('scenes_count', 0)}")
            print(f"   监控设备: {status.get('devices_count', 0)}")
            print(f"   唤醒设备: {status.get('wol_devices_count', 0)}")
        else:
            print(f"❌ 系统状态获取失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 系统状态获取失败: {e}")
    
    # 测试2: 获取场景列表
    print("\n2. 测试场景列表获取...")
    try:
        response = requests.get(f"{server_url}/api/scenes", timeout=5)
        if response.status_code == 200:
            scenes = response.json()
            print(f"✅ 场景列表获取成功，共 {len(scenes)} 个场景:")
            for i, scene in enumerate(scenes, 1):
                print(f"   {i}. {scene.get('name', '未知场景')}")
        else:
            print(f"❌ 场景列表获取失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 场景列表获取失败: {e}")
    
    # 测试3: 网络唤醒
    print("\n3. 测试网络唤醒...")
    try:
        response = requests.get(f"{server_url}/api/wake_all", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 网络唤醒测试成功: {result.get('message', '已发送唤醒信号')}")
        else:
            print(f"❌ 网络唤醒测试失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 网络唤醒测试失败: {e}")
    
    # 测试4: 停止场景
    print("\n4. 测试停止场景...")
    try:
        response = requests.get(f"{server_url}/api/stop", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 停止场景测试成功: {result.get('message', '场景已停止')}")
        else:
            print(f"❌ 停止场景测试失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 停止场景测试失败: {e}")
    
    # 测试5: 播放场景（如果有场景的话）
    print("\n5. 测试播放场景...")
    try:
        # 先获取场景列表
        response = requests.get(f"{server_url}/api/scenes", timeout=5)
        if response.status_code == 200:
            scenes = response.json()
            if scenes:
                scene_name = scenes[0].get('name', '')
                if scene_name:
                    response = requests.get(f"{server_url}/api/play?scene={scene_name}", timeout=5)
                    if response.status_code == 200:
                        result = response.json()
                        print(f"✅ 播放场景测试成功: {result.get('message', '场景开始播放')}")
                    else:
                        print(f"❌ 播放场景测试失败: HTTP {response.status_code}")
                else:
                    print("⚠️ 没有可播放的场景")
            else:
                print("⚠️ 场景列表为空")
        else:
            print(f"❌ 获取场景列表失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 播放场景测试失败: {e}")
    
    print("\n🎉 远程控制功能测试完成！")

def test_web_interface():
    """测试Web界面"""
    server_url = "http://192.168.1.3:8080"
    
    print("\n🌐 Web界面测试")
    print("=" * 40)
    
    try:
        response = requests.get(server_url, timeout=5)
        if response.status_code == 200:
            print("✅ Web界面访问成功")
            print(f"   响应长度: {len(response.text)} 字符")
            print(f"   内容类型: {response.headers.get('content-type', '未知')}")
            
            # 检查关键内容
            if "演出控制中心" in response.text:
                print("✅ 页面标题正确")
            if "场景控制" in response.text:
                print("✅ 场景控制模块存在")
            if "网络唤醒" in response.text:
                print("✅ 网络唤醒模块存在")
        else:
            print(f"❌ Web界面访问失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Web界面访问失败: {e}")

if __name__ == '__main__':
    test_remote_control()
    test_web_interface()
    
    print("\n📱 移动端测试说明:")
    print("1. 在手机浏览器中访问: http://192.168.1.3:8080")
    print("2. 或使用平板控制器连接到服务器")
    print("3. 测试各项功能是否正常工作")
    print("\n💡 提示: 确保设备连接到同一WiFi网络")
