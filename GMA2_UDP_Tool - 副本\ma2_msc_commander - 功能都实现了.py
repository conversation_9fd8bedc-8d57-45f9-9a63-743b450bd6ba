import socket
import threading
import customtkinter as ctk
import json
import os
import platform
import subprocess

try:
    from scapy.all import srp, Ether, ARP
    SCAPY_AVAILABLE = True
except (ImportError, OSError):
    SCAPY_AVAILABLE = False

# =============================================================================
#                             核心功能模块
# =============================================================================
def scan_local_network(callback):
    def run_scan():
        try:
            callback("update_status", "正在扫描，请稍候...")
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80)); local_ip = s.getsockname()[0]
            subnet = ".".join(local_ip.split('.')[:-1]) + ".0/24"
            ans, _ = srp(Ether(dst="ff:ff:ff:ff:ff:ff") / ARP(pdst=subnet), timeout=3, verbose=0)
            clients = [{'ip': r.psrc, 'mac': r.hwsrc} for s, r in ans]
            status = f"扫描完成，发现 {len(clients)} 个设备。" if clients else "未发现任何设备。"
            callback("update_status", status); callback("update_list", clients)
        except Exception as e:
            callback("update_status", f"扫描错误: {e}")
        finally:
            callback("scan_finished", None)
    threading.Thread(target=run_scan, daemon=True).start()

def test_ip_connection(ip, status_callback):
    def run_ping():
        try:
            status_callback(f"正在测试 {ip} ...", "gray")
            param = '-n' if platform.system().lower() == 'windows' else '-c'
            command = ['ping', param, '1', '-w', '1', ip]
            startupinfo = None
            if platform.system().lower() == 'windows':
                startupinfo = subprocess.STARTUPINFO(); startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            if subprocess.call(command, startupinfo=startupinfo) == 0:
                status_callback(f"成功: {ip} 在线且可达。", "green")
            else:
                status_callback(f"失败: {ip} 无响应或不存在。", "orange")
        except Exception as e:
            status_callback(f"测试出错: {e}", "red")
    threading.Thread(target=run_ping, daemon=True).start()

def build_and_send_gma_msc(app, status_callback):
    def run_send():
        try:
            ip = app.target_ip_entry.get();
            if not ip: raise ValueError("IP地址不能为空")
            port = int(app.port_entry.get() or '6004')
            device_id = int(app.device_id_entry.get() or '0')
            cmd_format = app.cmd_format_map[app.cmd_format_combo.get()]
            command_type = app.cmd_type_combo.get()

            msc_payload = bytearray([0xF0, 0x7F, device_id, 0x02, cmd_format])
            cmd_map = {"Go": 0x01, "Stop": 0x02, "Resume": 0x03, "Timed_Go": 0x04, "Set": 0x06, "Fire": 0x07, "Go_Off": 0x0B}
            msc_payload.append(cmd_map[command_type])

            if command_type in ["Go", "Stop", "Resume", "Go_Off"]:
                cue_path = app.exec_cue_path_entry.get(); exec_path_input = app.exec_path_entry.get()
                if cue_path: msc_payload.extend(cue_path.encode('ascii'))
                if exec_path_input:
                    if cue_path: msc_payload.append(0x00)
                    if '.' in exec_path_input:
                        page, executor = exec_path_input.split('.', 1); corrected_path = f"{executor}.{page}"
                        msc_payload.extend(corrected_path.encode('ascii'))
                    else: msc_payload.extend(exec_path_input.encode('ascii'))
            elif command_type == "Timed_Go":
                h, m, s = int(app.timedgo_h_entry.get() or 0), int(app.timedgo_m_entry.get() or 0), int(app.timedgo_s_entry.get() or 0)
                msc_payload.extend([h, m, s, 0x00, 0x00])
                cue_path, exec_path_input = app.timedgo_cue_path_entry.get(), app.timedgo_exec_path_entry.get()
                if cue_path: msc_payload.extend(cue_path.encode('ascii'))
                if exec_path_input:
                    if cue_path: msc_payload.append(0x00)
                    if '.' in exec_path_input:
                        page, executor = exec_path_input.split('.', 1); corrected_path = f"{executor}.{page}"
                        msc_payload.extend(corrected_path.encode('ascii'))
                    else: msc_payload.extend(exec_path_input.encode('ascii'))
            elif command_type == "Set":
                fader = int(app.set_fader_entry.get() or '1') - 1; page = int(app.set_page_entry.get() or '1')
                msc_payload.extend([fader, page])
                percent = float(app.set_pos_entry.get() or '0')
                val = percent * 1.28; coarse = int(val); fine = int((val - coarse) * 128)
                msc_payload.extend([fine, coarse])
                h, m, s = app.set_h_entry.get(), app.set_m_entry.get(), app.set_s_entry.get()
                if h or m or s:
                    msc_payload.extend([int(h or 0), int(m or 0), int(s or 0), 0x00, 0x00])
            elif command_type == "Fire":
                macro_num = int(app.fire_macro_entry.get() or '0')
                if not (1 <= macro_num <= 255): raise ValueError("宏编号必须在 1-255 之间")
                msc_payload.append(macro_num)

            msc_payload.append(0xF7)
            gma_header = bytearray(b'\x47\x4D\x41\x00\x4D\x53\x43\x00')
            total_length = 12 + len(msc_payload)
            final_packet = gma_header + total_length.to_bytes(4, 'little') + msc_payload
            
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock: sock.sendto(final_packet, (ip, port))
            hex_string = ' '.join(f'{b:02X}' for b in final_packet)
            status_callback(f"成功发送到 {ip}:{port}", hex_string, "green")
        except ValueError as e: status_callback(f"输入错误: {e}", "", "orange")
        except Exception as e: status_callback(f"构建或发送时出错: {e}", "", "red")
        finally: app.send_button.configure(state="normal")
    threading.Thread(target=run_send, daemon=True).start()

# =============================================================================
#                          图形用户界面 (GUI)
# =============================================================================
class App(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("grandMA2 全功能指令工具 (V10 - 终极版)"); self.geometry("850x750")
        self.grid_columnconfigure(1, weight=1); self.grid_rowconfigure(0, weight=1)
        self.protocol("WM_DELETE_WINDOW", self.on_closing); self.config_file = "gma2_tool_config.json"
        
        left_frame = ctk.CTkFrame(self, width=280); left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew"); left_frame.grid_rowconfigure(1, weight=1)
        self.scan_button = ctk.CTkButton(left_frame, text="扫描局域网", command=self.on_scan); self.scan_button.pack(fill="x", padx=10, pady=10)
        self.scan_status_label = ctk.CTkLabel(left_frame, text="点击扫描发现设备"); self.scan_status_label.pack(fill="x", padx=10, pady=5)
        self.device_list_frame = ctk.CTkScrollableFrame(left_frame, label_text="在线设备"); self.device_list_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        right_frame = ctk.CTkFrame(self); right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew"); right_frame.grid_columnconfigure(0, weight=1)
        signature_frame = ctk.CTkFrame(right_frame, fg_color="transparent"); signature_frame.pack(fill="x", pady=(0, 10))
        ctk.CTkLabel(signature_frame, text="软件设计: 徐小龙", font=("", 12)).pack(side="left", padx=10)
        ctk.CTkLabel(signature_frame, text="电话: 15692899229", font=("", 12)).pack(side="right", padx=10)

        common_frame = ctk.CTkFrame(right_frame); common_frame.pack(fill="x", padx=10, pady=5); common_frame.grid_columnconfigure(1, weight=1)
        ctk.CTkLabel(common_frame, text="目标IP:").grid(row=0, column=0, padx=5, pady=2, sticky="w"); self.target_ip_entry = ctk.CTkEntry(common_frame); self.target_ip_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        self.test_conn_button = ctk.CTkButton(common_frame, text="测试连接", width=80, command=self.on_test_connection); self.test_conn_button.grid(row=0, column=2, padx=5, pady=2)
        ctk.CTkLabel(common_frame, text="端口:").grid(row=1, column=0, padx=5, pady=2, sticky="w"); self.port_entry = ctk.CTkEntry(common_frame); self.port_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=2, sticky="ew")
        ctk.CTkLabel(common_frame, text="Device ID:").grid(row=2, column=0, padx=5, pady=2, sticky="w"); self.device_id_entry = ctk.CTkEntry(common_frame); self.device_id_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=2, sticky="ew")
        ctk.CTkLabel(common_frame, text="Cmd Fmt:").grid(row=3, column=0, padx=5, pady=2, sticky="w"); self.cmd_format_map = {"General Light": 0x01, "Moving Light": 0x02, "All": 0x7F}; self.cmd_format_combo = ctk.CTkComboBox(common_frame, values=list(self.cmd_format_map.keys())); self.cmd_format_combo.grid(row=3, column=1, columnspan=2, padx=5, pady=2, sticky="ew")
        
        ctk.CTkLabel(right_frame, text="指令类型 (Command Type):").pack(anchor="w", padx=20, pady=(10,0))
        self.cmd_type_map = {"Go":0x01, "Stop":0x02, "Resume":0x03, "Timed_Go":0x04, "Set":0x06, "Fire":0x07, "Go_Off":0x0B}
        self.cmd_type_combo = ctk.CTkComboBox(right_frame, values=list(self.cmd_type_map.keys()), command=self.on_command_change); self.cmd_type_combo.pack(fill="x", padx=10, pady=5)
        self.params_container = ctk.CTkFrame(right_frame, fg_color="transparent"); self.params_container.pack(fill="both", expand=True, padx=10, pady=5)
        self.create_param_frames()
        
        self.send_button = ctk.CTkButton(right_frame, text="构建并发送指令", height=40, command=self.on_send); self.send_button.pack(fill="x", padx=10, pady=10)
        ctk.CTkLabel(right_frame, text="完整UDP包预览 (可复制):").pack(anchor="w", padx=20, pady=(5,0))
        self.hex_preview_entry = ctk.CTkEntry(right_frame, font=("Courier New", 12)); self.hex_preview_entry.pack(fill="x", padx=10, pady=5)
        self.main_status_label = ctk.CTkLabel(right_frame, text="就绪"); self.main_status_label.pack(anchor="w", padx=10, pady=5)
        
        self.about_button = ctk.CTkButton(left_frame, text="关于", command=self.show_about_window); self.about_button.pack(side="bottom", fill="x", padx=10, pady=(0,10))
        self.check_scan_availability(); self.load_settings(); self.on_command_change()

    def create_param_frames(self):
        self.param_frames, self.command_hints = {}, {}
        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["ExecCommands"] = frame
        ctk.CTkLabel(frame, text="Cue 路径:").pack(anchor="w"); self.exec_cue_path_entry = ctk.CTkEntry(frame, placeholder_text="例如: 5 或 4.2"); self.exec_cue_path_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="执行器路径 (可选):").pack(anchor="w", pady=(5,0)); self.exec_path_entry = ctk.CTkEntry(frame, placeholder_text="格式: 页.执行器, 例如: 1.102"); self.exec_path_entry.pack(fill="x")
        self.command_hints["Go"] = "用途: 立即触发一个执行器上的指定Cue。\n参数: 'Cue路径'是必须的; '执行器路径'可选, 留空则作用于默认执行器。\n示例: 触发第1页第2号执行器上的Cue 5, 请在Cue路径填'5', 执行器路径填'1.2'。"
        self.command_hints["Stop"] = "用途: 暂停一个执行器。\n参数: '执行器路径'可选, 留空则暂停当前默认执行器。\n示例: 暂停第1页第2号执行器, 请在执行器路径填'1.2'。"
        self.command_hints["Resume"] = "用途: 继续一个被暂停的执行器。\n示例: 与Stop用法相同。"
        self.command_hints["Go_Off"] = "用途: 关闭一个执行器。\n参数: '执行器路径'是必须的。'Cue路径'通常设为'0'。\n示例: 关闭第1页第2号执行器, 请在Cue路径填'0', 执行器路径填'1.2'。"

        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["Timed_Go"] = frame
        ctk.CTkLabel(frame, text="Cue 路径:").pack(anchor="w"); self.timedgo_cue_path_entry = ctk.CTkEntry(frame); self.timedgo_cue_path_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="执行器路径 (可选):").pack(anchor="w"); self.timedgo_exec_path_entry = ctk.CTkEntry(frame, placeholder_text="格式: 页.执行器"); self.timedgo_exec_path_entry.pack(fill="x")
        time_frame = ctk.CTkFrame(frame); time_frame.pack(fill="x", pady=5); ctk.CTkLabel(time_frame, text="淡入时间:").pack(side="left")
        self.timedgo_h_entry = ctk.CTkEntry(time_frame, width=60, placeholder_text="时"); self.timedgo_h_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.timedgo_m_entry = ctk.CTkEntry(time_frame, width=60, placeholder_text="分"); self.timedgo_m_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.timedgo_s_entry = ctk.CTkEntry(time_frame, width=60, placeholder_text="秒"); self.timedgo_s_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.command_hints["Timed_Go"] = "用途: 用一个指定的时间来触发一个Cue。\n示例: 用5秒时间跳转到默认执行器上的Cue 10, 请在时间填'0, 0, 5', Cue路径填'10'。"
        
        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["Set"] = frame
        ctk.CTkLabel(frame, text="推子编号 (Fader #):").pack(anchor="w"); self.set_fader_entry = ctk.CTkEntry(frame); self.set_fader_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="页面编号 (Page #):").pack(anchor="w"); self.set_page_entry = ctk.CTkEntry(frame); self.set_page_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="位置 (%):").pack(anchor="w"); self.set_pos_entry = ctk.CTkEntry(frame, placeholder_text="0-100"); self.set_pos_entry.pack(fill="x")
        time_frame_set = ctk.CTkFrame(frame); time_frame_set.pack(fill="x", pady=5); ctk.CTkLabel(time_frame_set, text="淡入时间(可选):").pack(side="left")
        self.set_h_entry = ctk.CTkEntry(time_frame_set, width=60, placeholder_text="时"); self.set_h_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.set_m_entry = ctk.CTkEntry(time_frame_set, width=60, placeholder_text="分"); self.set_m_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.set_s_entry = ctk.CTkEntry(time_frame_set, width=60, placeholder_text="秒"); self.set_s_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.command_hints["Set"] = "用途: 将推子移动到指定位置, 可选附加淡入时间。\n示例: 将第2页第3号推子在5秒内移动到45%, 请填入推子=3, 页面=2, 位置=45, 时间=0,0,5。"
        
        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["Fire"] = frame
        ctk.CTkLabel(frame, text="宏编号 (Macro #):").pack(anchor="w"); self.fire_macro_entry = ctk.CTkEntry(frame, placeholder_text="1-255"); self.fire_macro_entry.pack(fill="x")
        self.command_hints["Fire"] = "用途: 触发一个宏 (Macro)。\n示例: 要触发第64号宏, 在'宏编号'中输入'64'。"
        
        self.hint_label = ctk.CTkLabel(self.params_container, text="", wraplength=450, justify="left", fg_color=("gray90", "gray20"), corner_radius=5); self.hint_label.pack(fill="x", pady=(10,0), ipady=10)

    def on_command_change(self, selected_cmd=None):
        if selected_cmd is None: selected_cmd = self.cmd_type_combo.get()
        exec_cmds = ["Go", "Stop", "Resume", "Go_Off"]
        current_frame_key = "ExecCommands" if selected_cmd in exec_cmds else selected_cmd
        for key, frame in self.param_frames.items():
            if key == current_frame_key: frame.pack(fill="both", pady=5, expand=True, before=self.hint_label)
            else: frame.pack_forget()
        self.hint_label.configure(text=self.command_hints.get(selected_cmd, ""))

    def on_send(self): self.send_button.configure(state="disabled"); build_and_send_gma_msc(self, self.update_status)
    def on_test_connection(self): self.test_conn_button.configure(state="disabled"); test_ip_connection(self.target_ip_entry.get(), self.update_main_status)
    def check_scan_availability(self):
        if not SCAPY_AVAILABLE: self.scan_button.configure(state="disabled"); self.scan_status_label.configure(text="扫描功能需要 Npcap 驱动。")
    def update_device_list(self, clients):
        for w in self.device_list_frame.winfo_children(): w.destroy()
        for c in clients:
            btn = ctk.CTkButton(self.device_list_frame, text=f"{c['ip']}\n{c['mac']}", fg_color="transparent", anchor="w", command=lambda ip=c['ip']: self.select_ip(ip))
            btn.pack(fill="x", padx=5, pady=2)
    def select_ip(self, ip): self.target_ip_entry.delete(0, "end"); self.target_ip_entry.insert(0, ip)
    def on_scan(self):
        self.scan_button.configure(state="disabled");
        for w in self.device_list_frame.winfo_children(): w.destroy()
        self.scan_status_label.configure(text=""); scan_local_network(self.handle_scan_callbacks)
    def handle_scan_callbacks(self, type, data):
        if type == "update_status": self.scan_status_label.configure(text=data)
        elif type == "update_list": self.update_device_list(data)
        elif type == "scan_finished": self.scan_button.configure(state="normal")
    def update_status(self, message, hex_string, color="white"):
        self.main_status_label.configure(text=message, text_color=color)
        if hex_string: self.hex_preview_entry.delete(0, "end"); self.hex_preview_entry.insert(0, hex_string)
    def update_main_status(self, message, color): self.main_status_label.configure(text=message, text_color=color); self.test_conn_button.configure(state="normal")
    def show_about_window(self):
        if getattr(self, "about_win", None) and self.about_win.winfo_exists(): self.about_win.focus(); return
        self.about_win = ctk.CTkToplevel(self); self.about_win.title("关于"); self.about_win.geometry("400x180")
        self.about_win.resizable(False, False); self.about_win.transient(self)
        ctk.CTkLabel(self.about_win, text="grandMA2 全功能指令工具", font=("", 20)).pack(pady=(20, 10))
        ctk.CTkLabel(self.about_win, text="软件设计: 徐小龙", font=("", 14)).pack(pady=5)
        ctk.CTkLabel(self.about_win, text="联系电话: 15692899229", font=("", 14)).pack(pady=5)
        ctk.CTkLabel(self.about_win, text="V10 - 终极版", font=("", 10), text_color="gray").pack(side="bottom", pady=10)
    def save_settings(self):
        # A more robust way to gather settings from various frames
        settings = {"target_ip_entry": self.target_ip_entry.get(), "port_entry": self.port_entry.get(),
                    "device_id_entry": self.device_id_entry.get(), "cmd_format_combo": self.cmd_format_combo.get(),
                    "cmd_type_combo": self.cmd_type_combo.get()}
        with open(self.config_file, 'w') as f: json.dump(settings, f, indent=4)
    def load_settings(self):
        if not os.path.exists(self.config_file): return
        try:
            with open(self.config_file, 'r') as f: settings = json.load(f)
            self.target_ip_entry.insert(0, settings.get("target_ip_entry", ""))
            self.port_entry.insert(0, settings.get("port_entry", "6004"))
            self.device_id_entry.insert(0, settings.get("device_id_entry", "0"))
            self.cmd_format_combo.set(settings.get("cmd_format_combo", "General Light"))
            self.cmd_type_combo.set(settings.get("cmd_type_combo", "Go"))
        except Exception as e: print(f"加载配置出错: {e}")
    def on_closing(self): self.save_settings(); self.destroy()

if __name__ == "__main__":
    app = App()
    app.mainloop()