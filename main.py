import tkinter as tk
from tkinter import ttk, messagebox
import socket
import re
import subprocess
import threading
from ipaddress import ip_network
from queue import Queue
import struct # 用于处理字节和长度转换

# --- 协议核心 (Protocol Core) - 基于官方文档的精确实现 ---
class MA2CommandBuilder:
    """
    根据官方grandMA2 MSC over Ethernet协议构建UDP数据包。
    """
    GMA_MSC_HEADER = b'\x47\x4d\x41\x00\x4d\x53\x43\x00' # "GMA\0MSC\0"

    def _text_to_hex_bytes(self, text):
        """将字符串文本 (如 "21.5") 转换为ASCII十六进制字节。"""
        return text.encode('ascii')

    def _build_msc_message(self, command_byte, data_bytes, device_id=0x7F, format_byte=0x7F):
        """构建核心的MSC消息 (F0...F7)。"""
        header = bytes([0xF0, 0x7F, device_id, 0x02, format_byte, command_byte])
        footer = bytes([0xF7])
        return header + data_bytes + footer

    def _wrap_with_gma_header(self, msc_message):
        """为MSC消息添加GMA UDP包头和长度。"""
        total_length = len(self.GMA_MSC_HEADER) + 4 + len(msc_message)
        # struct.pack '<I' 将长度打包为4字节小端序整数
        length_bytes = struct.pack('<I', total_length)
        return self.GMA_MSC_HEADER + length_bytes + msc_message

    def go_cue(self, cue_string, exec_string=None):
        """
        构建 Go Cue 指令 (Command 0x01)。
        cue_string: "1", "21.5", "37.200"
        exec_string: "5.1" (Page.Executor) or None for default
        """
        # 格式化Cue号，确保有三位小数
        if '.' not in cue_string:
            cue_string += '.000'
        
        data_bytes = self._text_to_hex_bytes(cue_string)
        
        if exec_string:
            # 如果指定了执行器，用 NULL (0x00) 分隔
            null_byte = b'\x00'
            exec_bytes = self._text_to_hex_bytes(exec_string)
            data_bytes += null_byte + exec_bytes
            
        msc_message = self._build_msc_message(0x01, data_bytes)
        return self._wrap_with_gma_header(msc_message)

    def fire_macro(self, macro_number):
        """
        构建 Fire Macro 指令 (Command 0x07)。
        macro_number: 1 to 255
        """
        if not 1 <= macro_number <= 255:
            raise ValueError("Macro number must be between 1 and 255.")
        
        # 宏编号直接作为单个字节数据
        data_bytes = bytes([macro_number])
        
        msc_message = self._build_msc_message(0x07, data_bytes)
        return self._wrap_with_gma_header(msc_message)

    def go_off_executor(self, exec_string):
        """
        构建 Go_Off 指令 (Command 0x0B)。
        exec_string: "9.5" (Page.Executor)
        """
        # Off命令通常使用一个虚拟的Cue号 "0.000"
        cue_bytes = self._text_to_hex_bytes("0.000")
        null_byte = b'\x00'
        exec_bytes = self._text_to_hex_bytes(exec_string)
        data_bytes = cue_bytes + null_byte + exec_bytes

        msc_message = self._build_msc_message(0x0B, data_bytes)
        return self._wrap_with_gma_header(msc_message)


# --- 网络功能 (Network Utilities) ---
def get_local_ip_and_subnet():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return ip_network(f'{local_ip}/24', strict=False)
    except Exception:
        return None

def ping_ip(ip, queue):
    try:
        subprocess.check_output(f"ping -n 1 -w 500 {ip}", shell=True, stderr=subprocess.DEVNULL)
        queue.put(ip)
    except subprocess.CalledProcessError:
        pass

def scan_network(progress_callback):
    network = get_local_ip_and_subnet()
    if not network:
        messagebox.showerror("网络错误", "无法确定本机IP地址和子网。")
        return []

    q = Queue()
    threads = []
    hosts_to_scan = list(network.hosts())
    
    progress_callback(0, len(hosts_to_scan))

    for i, host in enumerate(hosts_to_scan):
        t = threading.Thread(target=ping_ip, args=(str(host), q))
        t.start()
        threads.append(t)
        if i % 10 == 0 or i == len(hosts_to_scan) - 1:
            progress_callback(i + 1, len(hosts_to_scan))

    for t in threads:
        t.join()

    progress_callback(len(hosts_to_scan), len(hosts_to_scan))
    
    import time
    time.sleep(2)

    try:
        result = subprocess.check_output("arp -a", shell=True).decode('gbk', errors='ignore')
        pattern = re.compile(r"(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\s+([0-9a-fA-F]{2}(?:[:-][0-9a-fA-F]{2}){5})")
        
        dynamic_matches = []
        for line in result.splitlines():
            if 'dynamic' in line.lower():
                match = pattern.search(line)
                if match:
                    dynamic_matches.append(match.groups())
        
        return dynamic_matches
    except Exception as e:
        messagebox.showerror("扫描失败", f"执行 'arp -a' 失败:\n{e}")
        return []

def test_connection(ip):
    try:
        subprocess.check_output(f"ping -n 1 -w 1000 {ip}", shell=True, stderr=subprocess.DEVNULL)
        messagebox.showinfo("成功", f"Ping {ip} 成功，设备在线！")
    except subprocess.CalledProcessError:
        messagebox.showerror("失败", f"Ping {ip} 失败，设备不在线或被防火墙阻挡。")
    except Exception as e:
        messagebox.showerror("错误", f"执行Ping命令时出错: {e}")

def send_udp_command(ip, port, command_bytes):
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.sendto(command_bytes, (ip, int(port)))
        messagebox.showinfo("成功", f"指令已发送到 {ip}:{port}")
    except socket.error as e:
        messagebox.showerror("发送失败", f"发送指令失败\n错误: {e}")

# --- 主应用 (Main Application) ---
class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("grandMA2 UDP Tool (v2 - Protocol Correct)")
        self.geometry("650x550")
        self.builder = MA2CommandBuilder()

        # --- 网络配置 ---
        network_frame = ttk.LabelFrame(self, text="网络配置")
        network_frame.pack(padx=10, pady=10, fill="x")

        ttk.Label(network_frame, text="IP 地址:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.ip_var = tk.StringVar(value="*************")
        ttk.Entry(network_frame, textvariable=self.ip_var, width=20).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(network_frame, text="端口:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.port_var = tk.StringVar(value="8000")
        ttk.Entry(network_frame, textvariable=self.port_var, width=8).grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Button(network_frame, text="Ping IP", command=self.test_connection_handler).grid(row=0, column=4, padx=5, pady=5)
        self.scan_button = ttk.Button(network_frame, text="扫描局域网", command=self.scan_network_handler)
        self.scan_button.grid(row=0, column=5, padx=5, pady=5)

        # --- 状态与进度 ---
        status_frame = ttk.LabelFrame(self, text="状态")
        status_frame.pack(padx=10, pady=5, fill="x")
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var)
        self.progress_bar.pack(fill="x", padx=5, pady=5)

        # --- 指令构建 ---
        command_frame = ttk.LabelFrame(self, text="指令构建")
        command_frame.pack(padx=10, pady=10, fill="x")

        ttk.Label(command_frame, text="指令类型:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.command_type_var = tk.StringVar(value="Go Cue")
        command_options = ["Go Cue", "Fire Macro", "Off Executor"]
        ttk.OptionMenu(command_frame, self.command_type_var, command_options[0], *command_options, command=self.update_param_labels).grid(row=0, column=1, padx=5, pady=5, sticky="w")

        self.param1_label = ttk.Label(command_frame, text="Cue 号:")
        self.param1_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.param1_var = tk.StringVar(value="1")
        self.param1_entry = ttk.Entry(command_frame, textvariable=self.param1_var, width=15)
        self.param1_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")

        self.param2_label = ttk.Label(command_frame, text="执行器 (页.号):")
        self.param2_label.grid(row=1, column=2, padx=5, pady=5, sticky="w")
        self.param2_var = tk.StringVar()
        self.param2_entry = ttk.Entry(command_frame, textvariable=self.param2_var, width=15)
        self.param2_entry.grid(row=1, column=3, padx=5, pady=5, sticky="w")

        ttk.Button(command_frame, text="生成并发送", command=self.generate_and_send_handler).grid(row=2, column=0, columnspan=4, padx=10, pady=10)
        self.update_param_labels() # 初始化标签

        # --- 指令输出 ---
        output_frame = ttk.LabelFrame(self, text="生成的十六进制指令 (可复制)")
        output_frame.pack(padx=10, pady=10, fill="both", expand=True)

        self.output_text = tk.Text(output_frame, height=10, wrap="word")
        self.output_text.pack(padx=5, pady=5, fill="both", expand=True)
        self.output_text.config(state="disabled")

    def update_param_labels(self, *args):
        cmd_type = self.command_type_var.get()
        if cmd_type == "Go Cue":
            self.param1_label.config(text="Cue 号:")
            self.param2_label.config(text="执行器 (页.号, 可选):")
            self.param2_label.grid()
            self.param2_entry.grid()
        elif cmd_type == "Fire Macro":
            self.param1_label.config(text="Macro 号 (1-255):")
            self.param2_label.grid_remove()
            self.param2_entry.grid_remove()
        elif cmd_type == "Off Executor":
            self.param1_label.config(text="执行器 (页.号):")
            self.param2_label.grid_remove()
            self.param2_entry.grid_remove()

    def test_connection_handler(self):
        ip = self.ip_var.get()
        if not ip:
            messagebox.showwarning("输入错误", "请输入IP地址。")
            return
        test_connection(ip)

    def scan_network_handler(self):
        self.scan_button.config(state="disabled")
        self.progress_bar.config(maximum=100)
        scan_thread = threading.Thread(target=self._execute_scan)
        scan_thread.start()

    def _execute_scan(self):
        devices = scan_network(self.update_progress)
        if devices:
            self.show_scan_results(devices)
        else:
            messagebox.showinfo("扫描完成", "未在局域网中发现任何设备。")
        self.scan_button.config(state="normal")
        self.update_progress(0, 1)

    def update_progress(self, current, total):
        if total > 0:
            percentage = (current / total) * 100
            self.progress_var.set(percentage)
            self.update_idletasks()

    def show_scan_results(self, devices):
        result_window = tk.Toplevel(self)
        result_window.title("局域网扫描结果")
        result_window.geometry("450x350")

        tree = ttk.Treeview(result_window, columns=("ip", "mac"), show="headings")
        tree.heading("ip", text="IP 地址")
        tree.column("ip", width=150)
        tree.heading("mac", text="MAC 地址")
        tree.column("mac", width=200)
        
        for ip, mac in devices:
            tree.insert("", "end", values=(ip, mac))
            
        scrollbar = ttk.Scrollbar(result_window, orient="vertical", command=tree.yview)
        tree.configure(yscroll=scrollbar.set)
        scrollbar.pack(side="right", fill="y")
        tree.pack(side="left", fill="both", expand=True)

    def generate_and_send_handler(self):
        cmd_type = self.command_type_var.get()
        param1 = self.param1_var.get()
        param2 = self.param2_var.get()
        ip = self.ip_var.get()
        port = self.port_var.get()

        if not all([cmd_type, param1, ip, port]):
            messagebox.showwarning("输入错误", "请填写所有网络和指令信息。")
            return

        command_bytes = None
        try:
            if cmd_type == "Go Cue":
                exec_param = param2 if param2 else None
                command_bytes = self.builder.go_cue(param1, exec_param)
            elif cmd_type == "Fire Macro":
                command_bytes = self.builder.fire_macro(int(param1))
            elif cmd_type == "Off Executor":
                command_bytes = self.builder.go_off_executor(param1)
        except ValueError as e:
            messagebox.showerror("参数错误", str(e))
            return
        except Exception as e:
            messagebox.showerror("生成失败", f"构建指令时发生未知错误: {e}")
            return
        
        if command_bytes:
            hex_string = ' '.join(f'{b:02X}' for b in command_bytes)
            self.output_text.config(state="normal")
            self.output_text.delete("1.0", tk.END)
            self.output_text.insert(tk.END, hex_string)
            self.output_text.config(state="disabled")
            send_udp_command(ip, port, command_bytes)

if __name__ == "__main__":
    app = App()
    app.mainloop()