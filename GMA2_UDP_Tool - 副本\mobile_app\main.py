#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多媒体演出控制中心 - 移动端APP
支持Android和iOS平台
"""

import json
import requests
from datetime import datetime
import threading
import time

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.popup import Popup
from kivy.uix.scrollview import ScrollView
from kivy.clock import Clock
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.uix.switch import Switch
from kivy.uix.progressbar import ProgressBar
from kivy.graphics import Color, Rectangle
from kivy.metrics import dp
from kivy.core.window import Window

# 设置窗口大小（开发时使用）
Window.size = (360, 640)

class ConnectionScreen(Screen):
    """连接设置界面"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'connection'
        
        layout = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(10))
        
        # 标题
        title = Label(text='🎭 演出控制中心', font_size='24sp', size_hint_y=None, height=dp(60))
        layout.add_widget(title)
        
        # 服务器地址输入
        layout.add_widget(Label(text='服务器地址:', size_hint_y=None, height=dp(40)))
        self.server_input = TextInput(
            text='http://192.168.1.100:8080',
            multiline=False,
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(self.server_input)
        
        # 连接按钮
        connect_btn = Button(
            text='连接服务器',
            size_hint_y=None,
            height=dp(50),
            background_color=(0.2, 0.6, 1, 1)
        )
        connect_btn.bind(on_press=self.connect_to_server)
        layout.add_widget(connect_btn)
        
        # 状态显示
        self.status_label = Label(
            text='请输入服务器地址并连接',
            size_hint_y=None,
            height=dp(40)
        )
        layout.add_widget(self.status_label)
        
        # 说明文字
        info_text = """使用说明：
1. 确保手机和电脑连接同一WiFi
2. 在电脑上启动远程控制服务器
3. 输入显示的服务器地址
4. 点击连接即可开始控制"""
        
        info_label = Label(
            text=info_text,
            text_size=(None, None),
            halign='left',
            valign='top'
        )
        layout.add_widget(info_label)
        
        self.add_widget(layout)
    
    def connect_to_server(self, instance):
        """连接到服务器"""
        server_url = self.server_input.text.strip()
        if not server_url:
            self.status_label.text = '请输入服务器地址'
            return
        
        if not server_url.startswith('http'):
            server_url = 'http://' + server_url
        
        self.status_label.text = '正在连接...'
        
        # 在后台线程中测试连接
        threading.Thread(target=self.test_connection, args=(server_url,)).start()
    
    def test_connection(self, server_url):
        """测试服务器连接"""
        try:
            response = requests.get(f"{server_url}/api/status", timeout=5)
            if response.status_code == 200:
                # 连接成功，切换到主界面
                app = App.get_running_app()
                app.server_url = server_url
                Clock.schedule_once(lambda dt: self.connection_success(), 0)
            else:
                Clock.schedule_once(lambda dt: self.connection_failed("服务器响应错误"), 0)
        except Exception as e:
            Clock.schedule_once(lambda dt: self.connection_failed(f"连接失败: {str(e)}"), 0)
    
    def connection_success(self):
        """连接成功"""
        self.status_label.text = '连接成功！'
        self.manager.current = 'main'
    
    def connection_failed(self, error_msg):
        """连接失败"""
        self.status_label.text = error_msg

class MainScreen(Screen):
    """主控制界面"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'main'
        self.scenes = []
        self.system_status = {}
        
        layout = BoxLayout(orientation='vertical', padding=dp(10), spacing=dp(10))
        
        # 顶部状态栏
        self.create_status_bar(layout)
        
        # 控制按钮区域
        self.create_control_buttons(layout)
        
        # 场景列表
        self.create_scene_list(layout)
        
        self.add_widget(layout)
        
        # 定时更新状态
        Clock.schedule_interval(self.update_status, 5)
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=dp(60))
        
        # 连接状态
        self.connection_status = Label(text='🟢 已连接', size_hint_x=0.5)
        status_layout.add_widget(self.connection_status)
        
        # 刷新按钮
        refresh_btn = Button(text='🔄', size_hint_x=0.2)
        refresh_btn.bind(on_press=self.refresh_data)
        status_layout.add_widget(refresh_btn)
        
        # 设置按钮
        settings_btn = Button(text='⚙️', size_hint_x=0.3)
        settings_btn.bind(on_press=self.show_settings)
        status_layout.add_widget(settings_btn)
        
        parent.add_widget(status_layout)
    
    def create_control_buttons(self, parent):
        """创建控制按钮"""
        control_layout = GridLayout(cols=2, size_hint_y=None, height=dp(100), spacing=dp(10))
        
        # 停止按钮
        stop_btn = Button(
            text='⏹ 停止场景',
            background_color=(1, 0.3, 0.3, 1)
        )
        stop_btn.bind(on_press=self.stop_scene)
        control_layout.add_widget(stop_btn)
        
        # 唤醒设备按钮
        wake_btn = Button(
            text='🔌 唤醒设备',
            background_color=(0.3, 0.8, 0.3, 1)
        )
        wake_btn.bind(on_press=self.wake_all_devices)
        control_layout.add_widget(wake_btn)
        
        parent.add_widget(control_layout)
    
    def create_scene_list(self, parent):
        """创建场景列表"""
        # 场景列表标题
        scenes_title = Label(text='🎬 场景列表', size_hint_y=None, height=dp(40), font_size='18sp')
        parent.add_widget(scenes_title)
        
        # 滚动视图
        scroll = ScrollView()
        self.scenes_layout = BoxLayout(orientation='vertical', spacing=dp(5), size_hint_y=None)
        self.scenes_layout.bind(minimum_height=self.scenes_layout.setter('height'))
        
        scroll.add_widget(self.scenes_layout)
        parent.add_widget(scroll)
        
        # 初始加载场景
        self.load_scenes()
    
    def load_scenes(self):
        """加载场景列表"""
        try:
            app = App.get_running_app()
            response = requests.get(f"{app.server_url}/api/scenes", timeout=5)
            if response.status_code == 200:
                self.scenes = response.json()
                self.update_scenes_display()
            else:
                self.show_message("加载场景失败", "无法获取场景列表")
        except Exception as e:
            self.show_message("网络错误", f"加载场景失败: {str(e)}")
    
    def update_scenes_display(self):
        """更新场景显示"""
        self.scenes_layout.clear_widgets()
        
        if not self.scenes:
            no_scenes = Label(text='暂无场景', size_hint_y=None, height=dp(50))
            self.scenes_layout.add_widget(no_scenes)
            return
        
        for scene in self.scenes:
            scene_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=dp(80), spacing=dp(5))
            
            # 场景名称
            scene_name = Label(
                text=scene.get('name', '未知场景'),
                font_size='16sp',
                size_hint_y=None,
                height=dp(30)
            )
            scene_layout.add_widget(scene_name)
            
            # 播放按钮
            play_btn = Button(
                text=f"▶ 播放 {scene.get('name', '')}",
                size_hint_y=None,
                height=dp(40),
                background_color=(0.2, 0.6, 1, 1)
            )
            play_btn.bind(on_press=lambda x, scene_name=scene.get('name'): self.play_scene(scene_name))
            scene_layout.add_widget(play_btn)
            
            self.scenes_layout.add_widget(scene_layout)
    
    def play_scene(self, scene_name):
        """播放场景"""
        try:
            app = App.get_running_app()
            response = requests.get(f"{app.server_url}/api/play?scene={scene_name}", timeout=5)
            if response.status_code == 200:
                result = response.json()
                self.show_message("播放成功", result.get('message', '场景开始播放'))
            else:
                self.show_message("播放失败", "无法播放场景")
        except Exception as e:
            self.show_message("网络错误", f"播放失败: {str(e)}")
    
    def stop_scene(self, instance):
        """停止场景"""
        try:
            app = App.get_running_app()
            response = requests.get(f"{app.server_url}/api/stop", timeout=5)
            if response.status_code == 200:
                result = response.json()
                self.show_message("停止成功", result.get('message', '场景已停止'))
            else:
                self.show_message("停止失败", "无法停止场景")
        except Exception as e:
            self.show_message("网络错误", f"停止失败: {str(e)}")
    
    def wake_all_devices(self, instance):
        """唤醒所有设备"""
        try:
            app = App.get_running_app()
            response = requests.get(f"{app.server_url}/api/wake_all", timeout=5)
            if response.status_code == 200:
                result = response.json()
                self.show_message("唤醒成功", result.get('message', '设备唤醒信号已发送'))
            else:
                self.show_message("唤醒失败", "无法唤醒设备")
        except Exception as e:
            self.show_message("网络错误", f"唤醒失败: {str(e)}")
    
    def update_status(self, dt):
        """更新系统状态"""
        try:
            app = App.get_running_app()
            if hasattr(app, 'server_url'):
                response = requests.get(f"{app.server_url}/api/status", timeout=3)
                if response.status_code == 200:
                    self.system_status = response.json()
                    self.connection_status.text = '🟢 已连接'
                else:
                    self.connection_status.text = '🔴 连接异常'
        except:
            self.connection_status.text = '🔴 连接断开'
    
    def refresh_data(self, instance):
        """刷新数据"""
        self.load_scenes()
        self.update_status(0)
    
    def show_settings(self, instance):
        """显示设置"""
        self.manager.current = 'connection'
    
    def show_message(self, title, message):
        """显示消息弹窗"""
        content = BoxLayout(orientation='vertical', spacing=dp(10))
        content.add_widget(Label(text=message))
        
        close_btn = Button(text='确定', size_hint_y=None, height=dp(40))
        content.add_widget(close_btn)
        
        popup = Popup(title=title, content=content, size_hint=(0.8, 0.4))
        close_btn.bind(on_press=popup.dismiss)
        popup.open()

class PerformanceControlApp(App):
    """演出控制APP主类"""
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.server_url = ""
    
    def build(self):
        """构建应用界面"""
        self.title = "演出控制中心"
        
        # 创建屏幕管理器
        sm = ScreenManager()
        
        # 添加屏幕
        sm.add_widget(ConnectionScreen())
        sm.add_widget(MainScreen())
        
        return sm

if __name__ == '__main__':
    PerformanceControlApp().run()
