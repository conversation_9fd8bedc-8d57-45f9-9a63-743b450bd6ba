#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络扫描功能测试脚本
"""

import socket
import subprocess
import platform
import ipaddress
import re
import time

def get_local_ip():
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def ping_host(ip):
    """Ping主机检查是否在线"""
    try:
        if platform.system().lower() == "windows":
            result = subprocess.run(['ping', '-n', '1', '-w', '1000', str(ip)], 
                                  capture_output=True, text=True, timeout=2)
        else:
            result = subprocess.run(['ping', '-c', '1', '-W', '1', str(ip)], 
                                  capture_output=True, text=True, timeout=2)
        return result.returncode == 0
    except:
        return False

def get_mac_address(ip):
    """获取指定IP的MAC地址"""
    try:
        if platform.system().lower() == "windows":
            # Windows使用arp命令
            result = subprocess.run(['arp', '-a', str(ip)], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                # 解析arp输出
                lines = result.stdout.split('\n')
                for line in lines:
                    if str(ip) in line:
                        # 查找MAC地址模式
                        mac_match = re.search(r'([0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}', line)
                        if mac_match:
                            mac = mac_match.group().replace('-', ':').upper()
                            return mac
        else:
            # Linux/Mac使用arp命令
            result = subprocess.run(['arp', '-n', str(ip)], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if str(ip) in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            mac = parts[2].upper()
                            if ':' in mac and len(mac) == 17:
                                return mac
        return "未知"
    except:
        return "未知"

def get_device_hostname(ip):
    """获取设备主机名"""
    try:
        hostname = socket.gethostbyaddr(str(ip))[0]
        return hostname
    except:
        return "未知"

def scan_network():
    """扫描局域网"""
    print("🔍 局域网设备扫描测试")
    print("=" * 50)
    
    # 获取本机IP和网络段
    local_ip = get_local_ip()
    print(f"本机IP: {local_ip}")
    
    try:
        network = ipaddress.IPv4Network(f"{local_ip}/24", strict=False)
        print(f"扫描网络段: {network}")
        print()
        
        # 扫描前几个IP地址作为测试
        test_ips = list(network.hosts())[:10]  # 只测试前10个IP
        
        print("开始扫描...")
        print(f"{'IP地址':<15} {'状态':<8} {'MAC地址':<18} {'主机名'}")
        print("-" * 60)
        
        online_devices = []
        
        for i, host in enumerate(test_ips):
            print(f"扫描 {host}...", end=" ")
            
            if ping_host(host):
                mac = get_mac_address(host)
                hostname = get_device_hostname(host)
                
                device_info = {
                    'ip': str(host),
                    'mac': mac,
                    'hostname': hostname,
                    'status': '在线'
                }
                
                online_devices.append(device_info)
                
                print(f"{str(host):<15} {'在线':<8} {mac:<18} {hostname}")
            else:
                print(f"{str(host):<15} {'离线':<8} {'--':<18} {'--'}")
        
        print()
        print(f"扫描完成！发现 {len(online_devices)} 个在线设备")
        
        if online_devices:
            print("\n📋 在线设备详情:")
            for device in online_devices:
                print(f"  IP: {device['ip']}")
                print(f"  MAC: {device['mac']}")
                print(f"  主机名: {device['hostname']}")
                print(f"  状态: {device['status']}")
                print("-" * 30)
        
        return online_devices
        
    except Exception as e:
        print(f"扫描失败: {e}")
        return []

def test_arp_command():
    """测试ARP命令"""
    print("\n🧪 ARP命令测试")
    print("=" * 30)
    
    local_ip = get_local_ip()
    print(f"测试获取本机 {local_ip} 的MAC地址...")
    
    try:
        if platform.system().lower() == "windows":
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=10)
            print("ARP表内容:")
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
        else:
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True, timeout=10)
            print("ARP表内容:")
            print(result.stdout)
            
    except Exception as e:
        print(f"ARP命令测试失败: {e}")

def main():
    """主函数"""
    print("🌐 网络扫描功能测试工具")
    print("=" * 50)
    
    # 测试基本网络功能
    local_ip = get_local_ip()
    print(f"✅ 本机IP获取: {local_ip}")
    
    # 测试ping功能
    print(f"✅ Ping测试: {ping_host('127.0.0.1')}")
    
    # 测试ARP命令
    test_arp_command()
    
    # 扫描网络
    devices = scan_network()
    
    print("\n💡 使用说明:")
    print("1. 在演出控制系统中点击'网络唤醒'按钮")
    print("2. 点击'扫描网络'按钮")
    print("3. 等待扫描完成，查看发现的设备")
    print("4. 点击'添加'按钮将设备添加到唤醒列表")
    print("5. 输入设备名称和描述信息")
    print("6. 保存后即可使用网络唤醒功能")

if __name__ == '__main__':
    main()
