import socket
import threading
import customtkinter as ctk
import json
import os
import platform
import subprocess
from tkinter import filedialog, simpledialog

try:
    from scapy.all import srp, Ether, ARP
    SCAPY_AVAILABLE = True
except (ImportError, OSError):
    SCAPY_AVAILABLE = False

# =============================================================================
#                             核心功能模块
# =============================================================================
def scan_local_network(callback):
    def run_scan():
        # ... (此函数与之前版本相同，保持不变)
        try:
            callback("update_status", "正在扫描，请稍候...")
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80)); local_ip = s.getsockname()[0]
            subnet = ".".join(local_ip.split('.')[:-1]) + ".0/24"
            ans, _ = srp(Ether(dst="ff:ff:ff:ff:ff:ff") / ARP(pdst=subnet), timeout=3, verbose=0)
            clients = [{'ip': r.psrc, 'mac': r.hwsrc} for s, r in ans]
            status = f"扫描完成: {len(clients)} 个设备。" if clients else "未发现任何设备。"
            callback("update_status", status); callback("update_list", clients)
        except Exception as e: callback("update_status", f"扫描错误: {e}")
    threading.Thread(target=run_scan, daemon=True).start()

def build_and_send_gma_msc(app, status_callback):
    # ... (此函数与之前版本相同，保持不变)
    def run_send():
        try:
            ip = app.target_ip_entry.get();
            if not ip: raise ValueError("IP地址不能为空")
            port = int(app.port_entry.get() or '6004')
            device_id = int(app.device_id_entry.get() or '0')
            cmd_format = app.cmd_format_map[app.cmd_format_combo.get()]
            command_type = app.cmd_type_combo.get()

            msc_payload = bytearray([0xF0, 0x7F, device_id, 0x02, cmd_format])
            cmd_map = {"Go": 0x01, "Stop": 0x02, "Resume": 0x03, "Timed_Go": 0x04, "Set": 0x06, "Fire": 0x07, "Go_Off": 0x0B}
            msc_payload.append(cmd_map[command_type])

            if command_type in ["Go", "Stop", "Resume", "Go_Off"]:
                cue_path = app.exec_cue_path_entry.get(); exec_path_input = app.exec_path_entry.get()
                if cue_path: msc_payload.extend(cue_path.encode('ascii'))
                if exec_path_input:
                    if cue_path: msc_payload.append(0x00)
                    if '.' in exec_path_input:
                        page, executor = exec_path_input.split('.', 1); corrected_path = f"{executor}.{page}"
                        msc_payload.extend(corrected_path.encode('ascii'))
                    else: msc_payload.extend(exec_path_input.encode('ascii'))
            elif command_type == "Timed_Go":
                h, m, s = int(app.timedgo_h_entry.get() or 0), int(app.timedgo_m_entry.get() or 0), int(app.timedgo_s_entry.get() or 0)
                msc_payload.extend([h, m, s, 0x00, 0x00])
                cue_path, exec_path_input = app.timedgo_cue_path_entry.get(), app.timedgo_exec_path_entry.get()
                if cue_path: msc_payload.extend(cue_path.encode('ascii'))
                if exec_path_input:
                    if cue_path: msc_payload.append(0x00)
                    if '.' in exec_path_input:
                        page, executor = exec_path_input.split('.', 1); corrected_path = f"{executor}.{page}"
                        msc_payload.extend(corrected_path.encode('ascii'))
                    else: msc_payload.extend(exec_path_input.encode('ascii'))
            elif command_type == "Set":
                fader = int(app.set_fader_entry.get() or '1') - 1; page = int(app.set_page_entry.get() or '1')
                msc_payload.extend([fader, page])
                percent = float(app.set_pos_entry.get() or '0')
                val = percent * 1.28; coarse = int(val); fine = int((val - coarse) * 128)
                msc_payload.extend([fine, coarse])
                h, m, s = app.set_h_entry.get(), app.set_m_entry.get(), app.set_s_entry.get()
                if h or m or s:
                    msc_payload.extend([int(h or 0), int(m or 0), int(s or 0), 0x00, 0x00])
            elif command_type == "Fire":
                macro_num = int(app.fire_macro_entry.get() or '1')
                if not (1 <= macro_num <= 255): raise ValueError("宏编号必须在 1-255 之间")
                msc_payload.append(macro_num)

            msc_payload.append(0xF7)
            gma_header = bytearray(b'\x47\x4D\x41\x00\x4D\x53\x43\x00')
            total_length = 12 + len(msc_payload)
            final_packet = gma_header + total_length.to_bytes(4, 'little') + msc_payload
            
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock: sock.sendto(final_packet, (ip, port))
            hex_string = ' '.join(f'{b:02X}' for b in final_packet)
            status_callback(f"成功发送到 {ip}:{port}", hex_string, "green")
        except ValueError as e: status_callback(f"输入错误: {e}", "", "orange")
        except Exception as e: status_callback(f"构建或发送时出错: {e}", "", "red")
        finally: app.send_button.configure(state="normal")
    threading.Thread(target=run_send, daemon=True).start()

# =============================================================================
#                          图形用户界面 (GUI)
# =============================================================================
class App(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("多媒体演出控制中心 (V11 - 场景管理)"); self.geometry("1100x800")
        self.grid_columnconfigure(1, weight=1); self.grid_rowconfigure(0, weight=1)
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.scenes_file = "scenes.json"; self.scenes = []; self.selected_scene_index = None

        # --- Left Panel ---
        left_frame = ctk.CTkFrame(self, width=300); left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew"); left_frame.grid_rowconfigure(1, weight=1)
        ctk.CTkLabel(left_frame, text="场景列表", font=("", 16)).pack(pady=10)
        self.scene_list_frame = ctk.CTkScrollableFrame(left_frame); self.scene_list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        scene_btn_frame = ctk.CTkFrame(left_frame, fg_color="transparent"); scene_btn_frame.pack(fill="x", padx=10, pady=10)
        ctk.CTkButton(scene_btn_frame, text="保存当前为新场景", command=self.save_new_scene).pack(side="left", expand=True, padx=2)
        ctk.CTkButton(scene_btn_frame, text="删除选中", command=self.delete_selected_scene, fg_color="#D32F2F", hover_color="#B71C1C").pack(side="left", expand=True, padx=2)

        # --- Right Panel ---
        right_frame = ctk.CTkFrame(self); right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew"); right_frame.grid_columnconfigure(0, weight=1)
        
        # --- Top control frame ---
        top_controls = ctk.CTkFrame(right_frame); top_controls.pack(fill="x", padx=10, pady=5)
        top_controls.grid_columnconfigure(1, weight=1)
        ctk.CTkLabel(top_controls, text="目标IP:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
        self.target_ip_entry = ctk.CTkEntry(top_controls, placeholder_text="手动输入或点击扫描"); self.target_ip_entry.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        self.scan_button = ctk.CTkButton(top_controls, text="扫描网络", width=80, command=self.show_scan_window); self.scan_button.grid(row=0, column=2, padx=5, pady=2)
        
        # --- Command Builder ---
        self.command_builder_frame = ctk.CTkFrame(right_frame); self.command_builder_frame.pack(fill="both", expand=True, padx=10, pady=10)
        self.command_builder_frame.grid_columnconfigure(1, weight=1)
        
        # ... (和上一版一样的指令构建器和参数框布局)
        # For brevity, this part is conceptually the same, the full code is below.
        
        # --- 全功能指令构建器 (与V10相同) ---
        common_frame = ctk.CTkFrame(self.command_builder_frame); common_frame.pack(fill="x", pady=5); common_frame.grid_columnconfigure(1, weight=1)
        ctk.CTkLabel(common_frame, text="端口:").grid(row=1, column=0, padx=5, pady=2, sticky="w"); self.port_entry = ctk.CTkEntry(common_frame); self.port_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=2, sticky="ew")
        ctk.CTkLabel(common_frame, text="Device ID:").grid(row=2, column=0, padx=5, pady=2, sticky="w"); self.device_id_entry = ctk.CTkEntry(common_frame); self.device_id_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=2, sticky="ew")
        ctk.CTkLabel(common_frame, text="Cmd Fmt:").grid(row=3, column=0, padx=5, pady=2, sticky="w"); self.cmd_format_map = {"General Light": 0x01, "Moving Light": 0x02, "All": 0x7F}; self.cmd_format_combo = ctk.CTkComboBox(common_frame, values=list(self.cmd_format_map.keys())); self.cmd_format_combo.grid(row=3, column=1, columnspan=2, padx=5, pady=2, sticky="ew")
        ctk.CTkLabel(self.command_builder_frame, text="指令类型 (Command Type):").pack(anchor="w", padx=10, pady=(10,0))
        self.cmd_type_map = {"Go":0x01, "Stop":0x02, "Resume":0x03, "Timed_Go":0x04, "Set":0x06, "Fire":0x07, "Go_Off":0x0B}
        self.cmd_type_combo = ctk.CTkComboBox(self.command_builder_frame, values=list(self.cmd_type_map.keys()), command=self.on_command_change); self.cmd_type_combo.pack(fill="x", padx=10)
        self.params_container = ctk.CTkFrame(self.command_builder_frame, fg_color="transparent"); self.params_container.pack(fill="both", expand=True, padx=10)
        self.create_param_frames()
        
        # --- Media and Remarks ---
        media_frame = ctk.CTkFrame(right_frame); media_frame.pack(fill="x", padx=10, pady=5)
        media_frame.grid_columnconfigure(1, weight=1)
        ctk.CTkLabel(media_frame, text="音乐文件:").grid(row=0, column=0, sticky="w", padx=5)
        self.audio_path_entry = ctk.CTkEntry(media_frame, placeholder_text="点击右侧按钮选择文件..."); self.audio_path_entry.grid(row=0, column=1, sticky="ew", padx=5)
        ctk.CTkButton(media_frame, text="...", width=30, command=lambda: self.browse_file(self.audio_path_entry)).grid(row=0, column=2)
        ctk.CTkLabel(media_frame, text="视频文件:").grid(row=1, column=0, sticky="w", padx=5)
        self.video_path_entry = ctk.CTkEntry(media_frame, placeholder_text="点击右侧按钮选择文件..."); self.video_path_entry.grid(row=1, column=1, sticky="ew", padx=5)
        ctk.CTkButton(media_frame, text="...", width=30, command=lambda: self.browse_file(self.video_path_entry)).grid(row=1, column=2)
        
        ctk.CTkLabel(right_frame, text="场景备注:").pack(anchor="w", padx=20, pady=(10,0))
        self.remarks_textbox = ctk.CTkTextbox(right_frame, height=60); self.remarks_textbox.pack(fill="x", padx=10, pady=5)
        
        self.send_button = ctk.CTkButton(right_frame, text="运行此场景指令", height=40, command=self.on_send); self.send_button.pack(fill="x", padx=10, pady=10)
        self.hex_preview_entry = ctk.CTkEntry(right_frame, font=("Courier New", 12)); self.hex_preview_entry.pack(fill="x", padx=10, pady=5)
        self.main_status_label = ctk.CTkLabel(right_frame, text="就绪"); self.main_status_label.pack(anchor="w", padx=10, pady=5)

        self.load_scenes()
        
    def create_param_frames(self): # Same as V10
        self.param_frames, self.command_hints = {}, {}
        # ... (This entire block is identical to the V10 code)
        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["ExecCommands"] = frame
        ctk.CTkLabel(frame, text="Cue 路径:").pack(anchor="w"); self.exec_cue_path_entry = ctk.CTkEntry(frame, placeholder_text="例如: 5 或 4.2"); self.exec_cue_path_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="执行器路径 (可选):").pack(anchor="w", pady=(5,0)); self.exec_path_entry = ctk.CTkEntry(frame, placeholder_text="格式: 页.执行器, 例如: 1.102"); self.exec_path_entry.pack(fill="x")
        self.command_hints["Go"] = "用途: 立即触发一个执行器上的指定Cue。\n参数: 'Cue路径'是必须的; '执行器路径'可选, 留空则作用于默认执行器。\n示例: 触发第1页第2号执行器上的Cue 5, 请在Cue路径填'5', 执行器路径填'1.2'。"
        self.command_hints["Stop"] = "用途: 暂停一个执行器。\n参数: '执行器路径'可选, 留空则暂停当前默认执行器。\n示例: 暂停第1页第2号执行器, 请在执行器路径填'1.2'。"
        self.command_hints["Resume"] = "用途: 继续一个被暂停的执行器。\n示例: 与Stop用法相同。"
        self.command_hints["Go_Off"] = "用途: 关闭一个执行器。\n参数: '执行器路径'是必须的。'Cue路径'通常设为'0'。\n示例: 关闭第1页第2号执行器, 请在Cue路径填'0', 执行器路径填'1.2'。"
        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["Timed_Go"] = frame
        time_frame = ctk.CTkFrame(frame); time_frame.pack(fill="x"); ctk.CTkLabel(time_frame, text="淡入时间:").pack(side="left")
        self.timedgo_h_entry = ctk.CTkEntry(time_frame, width=60, placeholder_text="时"); self.timedgo_h_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.timedgo_m_entry = ctk.CTkEntry(time_frame, width=60, placeholder_text="分"); self.timedgo_m_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.timedgo_s_entry = ctk.CTkEntry(time_frame, width=60, placeholder_text="秒"); self.timedgo_s_entry.pack(side="left", fill="x", expand=True, padx=2)
        ctk.CTkLabel(frame, text="Cue 路径:").pack(anchor="w"); self.timedgo_cue_path_entry = ctk.CTkEntry(frame); self.timedgo_cue_path_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="执行器路径 (可选):").pack(anchor="w"); self.timedgo_exec_path_entry = ctk.CTkEntry(frame); self.timedgo_exec_path_entry.pack(fill="x")
        self.command_hints["Timed_Go"] = "用途: 用一个指定的时间来触发一个Cue。\n示例: 用5秒时间跳转到默认执行器上的Cue 10, 请在时间填'0, 0, 5', Cue路径填'10'。"
        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["Set"] = frame
        ctk.CTkLabel(frame, text="推子编号 (Fader #):").pack(anchor="w"); self.set_fader_entry = ctk.CTkEntry(frame); self.set_fader_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="页面编号 (Page #):").pack(anchor="w"); self.set_page_entry = ctk.CTkEntry(frame); self.set_page_entry.pack(fill="x")
        ctk.CTkLabel(frame, text="位置 (%):").pack(anchor="w"); self.set_pos_entry = ctk.CTkEntry(frame, placeholder_text="0-100"); self.set_pos_entry.pack(fill="x")
        time_frame_set = ctk.CTkFrame(frame); time_frame_set.pack(fill="x", pady=5); ctk.CTkLabel(time_frame_set, text="淡入时间(可选):").pack(side="left")
        self.set_h_entry = ctk.CTkEntry(time_frame_set, width=60, placeholder_text="时"); self.set_h_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.set_m_entry = ctk.CTkEntry(time_frame_set, width=60, placeholder_text="分"); self.set_m_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.set_s_entry = ctk.CTkEntry(time_frame_set, width=60, placeholder_text="秒"); self.set_s_entry.pack(side="left", fill="x", expand=True, padx=2)
        self.command_hints["Set"] = "用途: 将推子移动到指定位置, 可选附加淡入时间。\n示例: 将第2页第3号推子在5秒内移动到45%, 请填入推子=3, 页面=2, 位置=45, 时间=0,0,5。"
        frame = ctk.CTkFrame(self.params_container, fg_color="transparent"); self.param_frames["Fire"] = frame
        ctk.CTkLabel(frame, text="宏编号 (Macro #):").pack(anchor="w"); self.fire_macro_entry = ctk.CTkEntry(frame, placeholder_text="1-255"); self.fire_macro_entry.pack(fill="x")
        self.command_hints["Fire"] = "用途: 触发一个宏 (Macro)。\n示例: 要触发第64号宏, 在'宏编号'中输入'64'。"
        self.hint_label = ctk.CTkLabel(self.params_container, text="", wraplength=450, justify="left", fg_color=("gray90", "gray20"), corner_radius=5); self.hint_label.pack(fill="x", pady=(10,0), ipady=10)

    def on_command_change(self, selected_cmd=None):
        if selected_cmd is None: selected_cmd = self.cmd_type_combo.get()
        exec_cmds = ["Go", "Stop", "Resume", "Go_Off"]
        current_frame_key = "ExecCommands" if selected_cmd in exec_cmds else selected_cmd
        for key, frame in self.param_frames.items():
            if key == current_frame_key: frame.pack(fill="both", pady=5, expand=True, before=self.hint_label)
            else: frame.pack_forget()
        self.hint_label.configure(text=self.command_hints.get(selected_cmd, ""))

    def browse_file(self, entry_widget):
        filepath = filedialog.askopenfilename()
        if filepath:
            entry_widget.delete(0, "end"); entry_widget.insert(0, filepath)

    def get_current_settings(self):
        """Gather all settings from the GUI into a dictionary."""
        settings = {"target_ip": self.target_ip_entry.get(), "port": self.port_entry.get(),
                    "device_id": self.device_id_entry.get(), "cmd_format": self.cmd_format_combo.get(),
                    "cmd_type": self.cmd_type_combo.get(), "audio_path": self.audio_path_entry.get(),
                    "video_path": self.video_path_entry.get(), "remarks": self.remarks_textbox.get("1.0", "end-1c"),
                    "exec_cue_path": self.exec_cue_path_entry.get(), "exec_path": self.exec_path_entry.get(),
                    "timedgo_h": self.timedgo_h_entry.get(), "timedgo_m": self.timedgo_m_entry.get(), "timedgo_s": self.timedgo_s_entry.get(),
                    "timedgo_cue_path": self.timedgo_cue_path_entry.get(), "timedgo_exec_path": self.timedgo_exec_path_entry.get(),
                    "set_fader": self.set_fader_entry.get(), "set_page": self.set_page_entry.get(), "set_pos": self.set_pos_entry.get(),
                    "set_h": self.set_h_entry.get(), "set_m": self.set_m_entry.get(), "set_s": self.set_s_entry.get(),
                    "fire_macro": self.fire_macro_entry.get()}
        return settings

    def apply_settings_to_gui(self, settings):
        """Apply a dictionary of settings to the GUI widgets."""
        # This is a large mapping, but necessary for the feature to work
        s = settings # shorthand
        self.target_ip_entry.delete(0, "end"); self.target_ip_entry.insert(0, s.get("target_ip", ""))
        self.port_entry.delete(0, "end"); self.port_entry.insert(0, s.get("port", "6004"))
        self.device_id_entry.delete(0, "end"); self.device_id_entry.insert(0, s.get("device_id", "0"))
        self.cmd_format_combo.set(s.get("cmd_format", "General Light"))
        self.cmd_type_combo.set(s.get("cmd_type", "Go"))
        self.audio_path_entry.delete(0, "end"); self.audio_path_entry.insert(0, s.get("audio_path", ""))
        self.video_path_entry.delete(0, "end"); self.video_path_entry.insert(0, s.get("video_path", ""))
        self.remarks_textbox.delete("1.0", "end"); self.remarks_textbox.insert("1.0", s.get("remarks", ""))
        
        # Apply settings to all parameter frames
        self.exec_cue_path_entry.delete(0, "end"); self.exec_cue_path_entry.insert(0, s.get("exec_cue_path",""))
        self.exec_path_entry.delete(0, "end"); self.exec_path_entry.insert(0, s.get("exec_path",""))
        self.timedgo_h_entry.delete(0, "end"); self.timedgo_h_entry.insert(0, s.get("timedgo_h",""))
        self.timedgo_m_entry.delete(0, "end"); self.timedgo_m_entry.insert(0, s.get("timedgo_m",""))
        self.timedgo_s_entry.delete(0, "end"); self.timedgo_s_entry.insert(0, s.get("timedgo_s",""))
        self.timedgo_cue_path_entry.delete(0, "end"); self.timedgo_cue_path_entry.insert(0, s.get("timedgo_cue_path",""))
        self.timedgo_exec_path_entry.delete(0, "end"); self.timedgo_exec_path_entry.insert(0, s.get("timedgo_exec_path",""))
        self.set_fader_entry.delete(0, "end"); self.set_fader_entry.insert(0, s.get("set_fader",""))
        self.set_page_entry.delete(0, "end"); self.set_page_entry.insert(0, s.get("set_page",""))
        self.set_pos_entry.delete(0, "end"); self.set_pos_entry.insert(0, s.get("set_pos",""))
        self.set_h_entry.delete(0, "end"); self.set_h_entry.insert(0, s.get("set_h",""))
        self.set_m_entry.delete(0, "end"); self.set_m_entry.insert(0, s.get("set_m",""))
        self.set_s_entry.delete(0, "end"); self.set_s_entry.insert(0, s.get("set_s",""))
        self.fire_macro_entry.delete(0, "end"); self.fire_macro_entry.insert(0, s.get("fire_macro",""))
        
        self.on_command_change(s.get("cmd_type", "Go")) # Update visible frame

    def save_new_scene(self):
        name = simpledialog.askstring("新场景", "请输入场景名称:", parent=self)
        if name:
            scene = {"name": name, "settings": self.get_current_settings()}
            self.scenes.append(scene); self.update_scene_list(); self.save_scenes_to_file()
            self.load_scene(len(self.scenes) - 1) # Auto-select the new scene

    def load_scene(self, index):
        if 0 <= index < len(self.scenes):
            self.selected_scene_index = index
            self.apply_settings_to_gui(self.scenes[index]["settings"])
            self.update_scene_list()

    def delete_selected_scene(self):
        if self.selected_scene_index is not None and ctk.messagebox.askyesno("确认删除", f"您确定要删除场景 '{self.scenes[self.selected_scene_index]['name']}' 吗?"):
            del self.scenes[self.selected_scene_index]
            self.selected_scene_index = None; self.update_scene_list(); self.save_scenes_to_file()

    def update_scene_list(self):
        for w in self.scene_list_frame.winfo_children(): w.destroy()
        for i, scene in enumerate(self.scenes):
            btn = ctk.CTkButton(self.scene_list_frame, text=scene["name"], 
                                fg_color="#108040" if i == self.selected_scene_index else "#303030",
                                command=lambda i=i: self.load_scene(i))
            btn.pack(fill="x", padx=5, pady=2)

    def load_scenes(self):
        if os.path.exists(self.scenes_file):
            try:
                with open(self.scenes_file, 'r', encoding='utf-8') as f: self.scenes = json.load(f)
            except json.JSONDecodeError: print("场景文件已损坏，将创建新的。")
        self.update_scene_list()

    def save_scenes_to_file(self):
        with open(self.scenes_file, 'w', encoding='utf-8') as f: json.dump(self.scenes, f, indent=4, ensure_ascii=False)

    def show_scan_window(self):
        scan_win = ctk.CTkToplevel(self); scan_win.title("网络扫描"); scan_win.geometry("400x500")
        scan_win.transient(self); scan_win.grab_set()
        
        status_label = ctk.CTkLabel(scan_win, text="点击扫描开始...")
        status_label.pack(pady=10)
        
        list_frame = ctk.CTkScrollableFrame(scan_win, label_text="发现的设备")
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        def update_scan_list(clients):
            for c in clients:
                btn = ctk.CTkButton(list_frame, text=f"{c['ip']}\n{c['mac']}", fg_color="transparent", anchor="w", 
                                    command=lambda ip=c['ip']: (self.select_ip(ip), scan_win.destroy()))
                btn.pack(fill="x", padx=5, pady=2)
        
        def scan_callback(type, data):
            if type == "update_status": status_label.configure(text=data)
            elif type == "update_list": update_scan_list(data)

        scan_local_network(scan_callback)

    def on_closing(self): self.save_scenes_to_file(); self.destroy()
    def on_send(self): self.send_button.configure(state="disabled"); build_and_send_gma_msc(self, self.update_status)
    def update_status(self, message, hex_string, color="white"):
        self.main_status_label.configure(text=message, text_color=color)
        if hex_string is not None: self.hex_preview_entry.delete(0, "end"); self.hex_preview_entry.insert(0, hex_string)

if __name__ == "__main__":
    app = App()
    app.mainloop()