#!/usr/bin/env python3
"""
启动脚本 - 带有明显提示的ma2_msc_commander启动器
"""

import sys
import os
import time
import traceback

def main():
    print("=" * 60)
    print("    多媒体演出控制中心 (V16 - 终极版) 启动器")
    print("=" * 60)
    print()
    
    print("正在检查环境...")
    
    # 检查文件是否存在
    script_dir = os.path.dirname(os.path.abspath(__file__))
    script_path = os.path.join(script_dir, "ma2_msc_commander.py")

    if not os.path.exists(script_path):
        print(f"❌ 错误: 找不到 {script_path}")
        print("请确保在正确的目录中运行此脚本")
        input("按回车键退出...")
        return
    
    print("✓ 找到主程序文件")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查关键依赖
    try:
        import customtkinter
        print("✓ CustomTkinter 已安装")
    except ImportError:
        print("❌ CustomTkinter 未安装")
        print("请运行: pip install customtkinter")
        input("按回车键退出...")
        return
    
    try:
        import apscheduler
        print("✓ APScheduler 已安装")
    except ImportError:
        print("❌ APScheduler 未安装")
        print("请运行: pip install apscheduler")
        input("按回车键退出...")
        return
    
    print()
    print("🚀 正在启动程序...")
    print("注意: 如果程序窗口没有出现在前台，请检查任务栏")
    print()
    
    try:
        # 添加脚本目录到Python路径
        sys.path.insert(0, script_dir)

        # 导入并运行主程序
        import ma2_msc_commander
        
        print("✓ 程序模块加载成功")
        print("✓ 正在创建用户界面...")
        
        # 创建应用程序实例
        app = ma2_msc_commander.App()
        
        print("✓ 界面创建完成")
        print("✓ 程序正在运行...")
        print()
        print("=" * 60)
        print("程序已启动！如果看不到窗口，请检查:")
        print("1. 任务栏是否有程序图标")
        print("2. 是否被其他窗口遮挡")
        print("3. 是否最小化到系统托盘")
        print("按 Ctrl+C 可以退出程序")
        print("=" * 60)
        
        # 确保窗口显示在前台
        app.lift()
        app.attributes('-topmost', True)
        app.after(100, lambda: app.attributes('-topmost', False))
        
        # 运行主循环
        app.mainloop()
        
        print("\n程序已正常退出")
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        print("\n请将上述错误信息发送给开发者")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
