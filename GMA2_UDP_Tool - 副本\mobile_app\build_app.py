#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动APP构建脚本
支持Android APK和Windows可执行文件生成
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查构建依赖"""
    print("🔍 检查构建依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    # 检查必要的包
    required_packages = ['kivy', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def install_buildozer():
    """安装Buildozer（Android构建工具）"""
    print("📦 安装Buildozer...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'buildozer'], check=True)
        print("✅ Buildozer安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ Buildozer安装失败")
        return False

def build_android_apk():
    """构建Android APK"""
    print("🤖 开始构建Android APK...")
    
    # 检查buildozer是否可用
    try:
        subprocess.run(['buildozer', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Buildozer未安装，正在安装...")
        if not install_buildozer():
            return False
    
    try:
        # 初始化buildozer（如果需要）
        if not os.path.exists('buildozer.spec'):
            print("❌ buildozer.spec文件不存在")
            return False
        
        # 构建APK
        print("🔨 正在构建APK，这可能需要较长时间...")
        result = subprocess.run(['buildozer', 'android', 'debug'], 
                              capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ Android APK构建成功！")
            print("📱 APK文件位置: bin/")
            return True
        else:
            print("❌ Android APK构建失败")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def build_windows_exe():
    """构建Windows可执行文件"""
    print("🪟 开始构建Windows可执行文件...")
    
    try:
        # 检查PyInstaller
        try:
            import PyInstaller
        except ImportError:
            print("📦 安装PyInstaller...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
        
        # 构建exe
        cmd = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--name=演出控制中心',
            '--icon=icon.ico',  # 如果有图标文件
            'main.py'
        ]
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ Windows可执行文件构建成功！")
            print("💻 EXE文件位置: dist/")
            return True
        else:
            print("❌ Windows可执行文件构建失败")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_icon():
    """创建应用图标"""
    print("🎨 创建应用图标...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建一个简单的图标
        size = (512, 512)
        img = Image.new('RGBA', size, (102, 126, 234, 255))  # 蓝色背景
        draw = ImageDraw.Draw(img)
        
        # 绘制简单的图标
        # 绘制一个圆形
        margin = 50
        draw.ellipse([margin, margin, size[0]-margin, size[1]-margin], 
                    fill=(255, 255, 255, 255), outline=(200, 200, 200, 255), width=10)
        
        # 添加文字
        try:
            font = ImageFont.truetype("arial.ttf", 80)
        except:
            font = ImageFont.load_default()
        
        text = "🎭"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size[0] - text_width) // 2
        y = (size[1] - text_height) // 2
        draw.text((x, y), text, fill=(102, 126, 234, 255), font=font)
        
        # 保存不同尺寸的图标
        img.save('icon.png')
        
        # 创建ICO文件（Windows）
        ico_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        ico_images = []
        for ico_size in ico_sizes:
            ico_img = img.resize(ico_size, Image.Resampling.LANCZOS)
            ico_images.append(ico_img)
        
        ico_images[0].save('icon.ico', format='ICO', sizes=[(img.width, img.height) for img in ico_images])
        
        print("✅ 应用图标创建成功")
        return True
        
    except ImportError:
        print("⚠️ PIL未安装，跳过图标创建")
        return True
    except Exception as e:
        print(f"⚠️ 图标创建失败: {e}")
        return True

def main():
    """主函数"""
    print("🚀 多媒体演出控制中心 - 移动APP构建工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的依赖包")
        return
    
    # 创建图标
    create_icon()
    
    print("\n请选择构建目标:")
    print("1. Android APK")
    print("2. Windows EXE")
    print("3. 两者都构建")
    print("4. 仅测试运行")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == '1':
        build_android_apk()
    elif choice == '2':
        build_windows_exe()
    elif choice == '3':
        print("🔄 构建Android APK...")
        build_android_apk()
        print("\n🔄 构建Windows EXE...")
        build_windows_exe()
    elif choice == '4':
        print("🧪 测试运行应用...")
        try:
            subprocess.run([sys.executable, 'main.py'])
        except KeyboardInterrupt:
            print("\n✅ 测试完成")
    else:
        print("❌ 无效选择")
    
    print("\n🎉 构建完成！")
    print("\n📋 使用说明:")
    print("1. 将生成的APK文件传输到Android设备安装")
    print("2. 或者直接运行Windows EXE文件")
    print("3. 确保设备与演出控制系统在同一网络")
    print("4. 在APP中输入服务器地址即可开始控制")

if __name__ == '__main__':
    main()
