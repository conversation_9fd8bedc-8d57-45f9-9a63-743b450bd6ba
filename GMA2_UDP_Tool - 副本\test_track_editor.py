#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轨道编辑器测试脚本
测试轨道编辑器的基本功能
"""

import sys
import os
import json
import customtkinter as ctk
from tkinter import messagebox

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主程序模块
from ma2_msc_commander import TrackTimelineWindow

class TestApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("轨道编辑器测试")
        self.root.geometry("400x300")
        
        # 加载测试数据
        self.load_test_data()
        
        # 创建界面
        self.create_interface()
        
    def load_test_data(self):
        """加载测试数据"""
        try:
            # 加载序列数据
            with open('sequences.json', 'r', encoding='utf-8') as f:
                sequences = json.load(f)
            
            # 加载场景数据
            with open('scenes.json', 'r', encoding='utf-8') as f:
                scenes = json.load(f)
            
            # 设置当前序列为第一个序列
            self.current_sequence = sequences[0] if sequences else None
            self.scenes = scenes
            
            print(f"✅ 加载了 {len(sequences)} 个序列和 {len(scenes)} 个场景")
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")
            self.current_sequence = None
            self.scenes = []
    
    def create_interface(self):
        """创建测试界面"""
        # 标题
        title_label = ctk.CTkLabel(self.root, text="轨道编辑器测试", 
                                  font=("", 20, "bold"))
        title_label.pack(pady=20)
        
        # 序列信息
        if self.current_sequence:
            info_text = f"当前序列: {self.current_sequence['name']}\n"
            info_text += f"步骤数: {len(self.current_sequence['steps'])}\n"
            info_text += f"总时长: {self.current_sequence['total_duration']}秒"
        else:
            info_text = "没有可用的序列数据"
        
        info_label = ctk.CTkLabel(self.root, text=info_text, 
                                 font=("", 12))
        info_label.pack(pady=10)
        
        # 测试按钮
        test_button = ctk.CTkButton(self.root, text="打开轨道编辑器", 
                                   width=200, height=40,
                                   command=self.open_track_editor)
        test_button.pack(pady=20)
        
        # 状态标签
        self.status_label = ctk.CTkLabel(self.root, text="准备就绪", 
                                        text_color="green")
        self.status_label.pack(pady=10)
    
    def open_track_editor(self):
        """打开轨道编辑器"""
        try:
            if not self.current_sequence:
                messagebox.showerror("错误", "没有可用的序列数据")
                return

            self.status_label.configure(text="正在打开轨道编辑器...", text_color="orange")
            self.root.update()

            # 创建一个简化的轨道编辑器窗口
            self.create_simple_track_editor()

            self.status_label.configure(text="轨道编辑器已打开", text_color="green")

        except Exception as e:
            error_msg = f"打开轨道编辑器失败: {e}"
            print(f"❌ {error_msg}")
            self.status_label.configure(text=error_msg, text_color="red")
            messagebox.showerror("错误", error_msg)

    def create_simple_track_editor(self):
        """创建简化的轨道编辑器用于测试"""
        # 创建新窗口
        track_window = ctk.CTkToplevel(self.root)
        track_window.title("轨道编辑器测试")
        track_window.geometry("1200x800")

        # 标题
        title_label = ctk.CTkLabel(track_window, text="🎬 轨道编辑器测试版",
                                  font=("", 18, "bold"))
        title_label.pack(pady=10)

        # 序列信息
        info_text = f"序列: {self.current_sequence['name']}\n"
        info_text += f"步骤数: {len(self.current_sequence['steps'])}\n"
        info_text += f"总时长: {self.format_time(self.current_sequence['total_duration'])}"

        info_label = ctk.CTkLabel(track_window, text=info_text, font=("", 12))
        info_label.pack(pady=10)

        # 轨道显示区域
        tracks_frame = ctk.CTkFrame(track_window)
        tracks_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 显示轨道信息
        for i, step in enumerate(self.current_sequence["steps"]):
            track_info = f"轨道 {i+1}: {step['scene_name']} (延迟: {step['delay']}秒)"
            track_label = ctk.CTkLabel(tracks_frame, text=track_info,
                                     font=("", 11), anchor="w")
            track_label.pack(fill="x", padx=10, pady=2)

        # 测试按钮
        test_frame = ctk.CTkFrame(track_window)
        test_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkButton(test_frame, text="✅ 轨道加载成功",
                     fg_color="green", state="disabled").pack(side="left", padx=5)

        ctk.CTkButton(test_frame, text="关闭",
                     command=track_window.destroy).pack(side="right", padx=5)
    
    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"
    
    def run(self):
        """运行测试应用"""
        print("🚀 启动轨道编辑器测试...")
        self.root.mainloop()

if __name__ == "__main__":
    # 设置CustomTkinter主题
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    # 创建并运行测试应用
    app = TestApp()
    app.run()
