# 监控设备管理功能说明

## 🎯 功能概述

监控设备管理功能是实时演出监控大屏的重要扩展，允许用户将各种市面上的IP摄像头、网络监控设备集成到演出监控系统中。通过统一的管理界面，运营人员可以实时查看演出现场的各个角度画面，实现真正的全方位监控。

## 📹 主要功能

### 1. 监控设备管理
- **功能位置**: 场景控制界面 → "📹 监控设备" 按钮
- **核心功能**:
  - 添加、编辑、删除监控设备
  - 支持多种主流监控设备品牌
  - 自动发现网络中的监控设备
  - 设备连接测试和状态监控

### 2. 监控画面查看器
- **功能位置**: 场景控制界面 → "🖼️ 监控画面" 按钮
- **核心功能**:
  - 多画面同时显示
  - 灵活的布局配置（1x1, 2x2, 3x3, 4x4）
  - 实时视频流播放
  - 画面切换和控制

## 🔧 支持的设备类型

### 主流监控品牌支持
我们内置了主流监控设备品牌的配置模板：

#### 1. 海康威视 (Hikvision)
- **默认端口**: 554
- **默认用户名**: admin
- **流地址模板**: `rtsp://admin:password@*************:554/Streaming/Channels/101`

#### 2. 大华 (Dahua)
- **默认端口**: 554
- **默认用户名**: admin
- **流地址模板**: `rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0`

#### 3. 宇视 (Uniview)
- **默认端口**: 554
- **默认用户名**: admin
- **流地址模板**: `rtsp://admin:password@*************:554/video1`

#### 4. 安讯士 (Axis)
- **默认端口**: 554
- **默认用户名**: root
- **流地址模板**: `rtsp://root:password@*************:554/axis-media/media.amp`

#### 5. 通用RTSP
- **默认端口**: 554
- **默认用户名**: admin
- **流地址模板**: `rtsp://admin:password@*************:554/stream1`

### 支持的协议
- **RTSP** (Real Time Streaming Protocol) - 主流协议
- **HTTP** (HTTP视频流)
- **ONVIF** (开放网络视频接口论坛标准)

## 🎛️ 监控设备管理界面

### 左侧控制面板
#### 设备操作区域
- **➕ 添加设备**: 手动添加新的监控设备
- **🔍 自动发现**: 自动扫描网络中的监控设备
- **📁 批量导入**: 批量导入设备配置文件
- **💾 导出配置**: 导出当前设备配置

#### 设备统计区域
- **总设备数**: 已配置的监控设备总数
- **在线设备**: 当前在线的设备数量
- **离线设备**: 当前离线的设备数量
- **活动流**: 正在播放的视频流数量

### 右侧设备列表
#### 设备信息显示
- **设备名称**: 用户自定义的设备名称
- **状态指示器**: 
  - 🟢 在线：设备可正常连接
  - 🔴 离线：设备无法连接
- **设备详情**: IP地址、端口、协议、安装位置

#### 设备操作按钮
- **🔗 测试**: 测试设备网络连接
- **▶ 启动/⏹ 停止**: 启动或停止视频流
- **✏️ 编辑**: 编辑设备配置信息
- **🗑️ 删除**: 删除设备配置

## 📝 添加监控设备

### 基本信息配置
- **设备名称**: 便于识别的设备名称（如：舞台监控摄像头）
- **安装位置**: 设备的物理安装位置（如：舞台正面）
- **设备描述**: 设备用途和特点的详细描述

### 网络配置
- **设备品牌**: 选择设备品牌，系统自动填充默认配置
- **IP地址**: 设备的网络IP地址
- **端口**: 设备的网络端口（通常为554）
- **协议**: 视频流协议（RTSP/HTTP/ONVIF）

### 认证信息
- **用户名**: 设备登录用户名
- **密码**: 设备登录密码

### 高级设置
- **流地址**: 完整的视频流URL地址
- **自动生成**: 根据品牌模板自动生成流地址
- **启用设备**: 是否启用此设备

## 🖼️ 监控画面查看器

### 顶部工具栏
- **布局选择**: 选择画面布局（单画面、2x2、3x3、4x4）
- **🔄 刷新**: 刷新设备列表和状态
- **⏹ 全部停止**: 停止所有正在播放的视频流

### 左侧设备面板
- **可用设备列表**: 显示所有已启用的监控设备
- **设备状态**: 实时显示设备在线状态
- **播放控制**: 每个设备的独立播放/停止按钮

### 右侧视频显示区域
- **网格布局**: 根据选择的布局显示视频画面
- **画面占位符**: 显示视频位置编号和操作提示
- **实时画面**: 显示正在播放的监控画面

### 底部状态栏
- **状态信息**: 显示当前操作状态和提示信息
- **关闭按钮**: 关闭监控画面查看器

## 🔍 自动发现功能

### 网络扫描
- **扫描范围**: 自动检测本机网段（如：192.168.1.x）
- **端口检测**: 扫描常见监控设备端口（554, 80, 8080, 8000）
- **设备识别**: 识别可能的监控设备

### 发现结果
- **设备列表**: 显示发现的设备IP和端口
- **手动添加**: 可基于发现结果手动添加设备
- **批量导入**: 支持批量导入发现的设备

## ⚙️ 技术特点

### 1. 多协议支持
- **RTSP协议**: 支持主流的RTSP视频流
- **HTTP协议**: 支持HTTP方式的视频流
- **ONVIF标准**: 支持ONVIF标准设备

### 2. 品牌适配
- **预设模板**: 内置主流品牌的配置模板
- **自动配置**: 选择品牌后自动填充默认参数
- **灵活定制**: 支持手动修改和自定义配置

### 3. 连接管理
- **连接测试**: 添加设备前可测试网络连接
- **状态监控**: 实时监控设备在线状态
- **自动重连**: 支持断线自动重连机制

### 4. 流媒体处理
- **多流并发**: 支持同时播放多路视频流
- **布局切换**: 灵活的画面布局切换
- **资源管理**: 智能的视频流资源管理

## 🎮 操作指南

### 添加监控设备
1. 点击"📹 监控设备"打开管理界面
2. 点击"➕ 添加设备"
3. 填写设备基本信息（名称、位置、描述）
4. 选择设备品牌（系统自动填充默认配置）
5. 输入网络配置（IP、端口、协议）
6. 输入认证信息（用户名、密码）
7. 点击"自动生成"生成流地址
8. 点击"🔗 测试连接"验证设备连接
9. 点击"添加设备"完成添加

### 查看监控画面
1. 点击"🖼️ 监控画面"打开查看器
2. 在左侧设备列表中选择要查看的设备
3. 点击设备的"▶ 播放"按钮启动视频流
4. 画面将显示在右侧的视频网格中
5. 可以选择不同的布局查看多个画面
6. 使用"⏹ 停止"按钮停止特定设备的视频流

### 设备管理操作
1. **编辑设备**: 点击"✏️ 编辑"修改设备配置
2. **测试连接**: 点击"🔗 测试"检查设备连接状态
3. **删除设备**: 点击"🗑️ 删除"移除设备配置
4. **批量操作**: 使用"⏹ 全部停止"等批量控制功能

## 📊 应用场景

### 演出现场监控
- **舞台监控**: 监控舞台表演区域
- **观众席监控**: 观察观众反应和安全状况
- **后台监控**: 监控演员准备和设备区域
- **入口监控**: 监控观众入场和疏散情况

### 设备安全监控
- **设备机房**: 监控重要设备的运行状态
- **存储区域**: 监控道具和设备存放区域
- **周边安全**: 监控演出场地周边安全

### 运营管理支持
- **实时指挥**: 为现场指挥提供全方位视角
- **应急响应**: 快速发现和响应突发情况
- **质量监控**: 监控演出质量和效果
- **数据记录**: 为后续分析提供视频资料

## 🔧 配置文件

### 设备配置文件 (camera_devices.json)
```json
[
  {
    "id": "camera_20241217_143022_0",
    "name": "演出舞台监控",
    "type": "ip_camera",
    "protocol": "rtsp",
    "ip": "*************",
    "port": 554,
    "username": "admin",
    "password": "admin123",
    "stream_url": "rtsp://admin:admin123@*************:554/stream1",
    "description": "舞台正面监控摄像头",
    "location": "舞台正面",
    "status": "offline",
    "enabled": true,
    "created_time": "2024-12-17T14:30:22"
  }
]
```

### 监控配置文件 (camera_config.json)
```json
{
  "default_layout": "grid_2x2",
  "stream_quality": "medium",
  "auto_reconnect": true,
  "connection_timeout": 10,
  "supported_protocols": ["rtsp", "http", "onvif"],
  "layouts": {
    "single": {"rows": 1, "cols": 1},
    "grid_2x2": {"rows": 2, "cols": 2},
    "grid_3x3": {"rows": 3, "cols": 3},
    "grid_4x4": {"rows": 4, "cols": 4}
  }
}
```

## 🚀 扩展功能

### 近期计划
1. **录像功能**: 支持监控画面录制和回放
2. **移动侦测**: 智能移动侦测和报警
3. **画面标注**: 支持在监控画面上添加标注
4. **多屏输出**: 支持将监控画面输出到外部显示器

### 长期规划
1. **AI分析**: 基于AI的人流分析和行为识别
2. **云端存储**: 支持监控数据云端存储
3. **移动端**: 手机/平板监控应用
4. **集成报警**: 与安防系统集成

## 💡 最佳实践

### 设备部署建议
1. **网络规划**: 确保监控设备与控制系统在同一网段
2. **带宽考虑**: 根据画面数量和质量规划网络带宽
3. **电源保障**: 确保监控设备有稳定的电源供应
4. **位置选择**: 选择最佳的监控角度和位置

### 使用技巧
1. **分组管理**: 按区域或功能对设备进行分组
2. **命名规范**: 使用清晰的命名规范便于管理
3. **定期测试**: 定期测试设备连接和画面质量
4. **备份配置**: 定期备份设备配置文件

## 🎉 总结

监控设备管理功能为实时演出监控大屏提供了强大的视频监控能力。通过：

- 🔧 **统一管理**: 集中管理各种品牌的监控设备
- 📹 **实时监控**: 多画面同时显示监控画面
- 🎛️ **灵活配置**: 支持多种布局和显示方式
- 🔍 **智能发现**: 自动发现网络中的监控设备

这个功能让演出监控系统具备了真正的"眼睛"，运营人员可以：
- 👀 **全方位观察**: 实时查看演出现场各个角度
- 🚨 **及时响应**: 快速发现和处理突发情况
- 📊 **数据支持**: 为运营决策提供视觉依据
- 🛡️ **安全保障**: 确保演出和观众安全

配合实时演出监控大屏的其他功能，形成了完整的智能化演出监控解决方案！🎊
