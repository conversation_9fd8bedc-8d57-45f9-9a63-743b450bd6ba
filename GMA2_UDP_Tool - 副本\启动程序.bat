@echo off
chcp 65001 >nul
title 多媒体演出控制中心 - 启动器

echo.
echo ============================================================
echo     多媒体演出控制中心 (V16 - 终极版)
echo ============================================================
echo.

cd /d "%~dp0"

echo 正在启动程序...
python ma2_msc_commander.py

if %errorlevel% neq 0 (
    echo.
    echo 程序启动失败，错误代码: %errorlevel%
    echo 请检查Python环境和依赖库是否正确安装
    echo.
    pause
) else (
    echo.
    echo 程序已正常退出
)
