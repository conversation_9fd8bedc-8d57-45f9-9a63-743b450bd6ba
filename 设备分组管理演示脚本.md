# 设备分组管理功能演示脚本

## 🎬 演示流程 (总时长: 8分钟)

### 第一部分：设备选择功能演示 (2分钟)

#### 1. 界面介绍 (30秒)
- **切换到设备监控标签**：展示设备监控界面
- **指出新增功能**：
  - 设备卡片上的选择框
  - 批量操作按钮区域（全选、清空、批量控制）
  - 设备分组显示区域

#### 2. 单个设备选择 (30秒)
- **点击选择框**：演示单个设备的选择
- **视觉反馈**：展示选中设备的蓝色边框
- **计数更新**：显示批量控制按钮的数字变化
- **取消选择**：演示取消选择的效果

#### 3. 批量选择功能 (1分钟)
- **全选操作**：点击"全选"按钮选择所有设备
- **选择状态**：展示所有设备都被选中的状态
- **清空选择**：点击"清空"按钮取消所有选择
- **部分选择**：手动选择几个设备进行后续演示

### 第二部分：设备分组创建 (2分钟)

#### 1. 创建第一个分组 (1分钟)
- **选择设备**：选择3-4个设备作为"舞台灯光"分组
- **创建分组**：点击"创建分组"按钮
- **输入名称**：输入"舞台灯光"作为分组名称
- **确认创建**：展示分组创建成功的消息
- **查看结果**：显示设备卡片上的分组标识

#### 2. 创建第二个分组 (1分钟)
- **选择其他设备**：选择剩余设备作为"音响系统"分组
- **快速创建**：演示快速创建分组的流程
- **分组列表**：展示左侧分组列表的更新
- **分组统计**：显示每个分组的设备数量

### 第三部分：批量控制面板 (3分钟)

#### 1. 打开批量控制面板 (30秒)
- **选择设备**：选择几个设备
- **打开面板**：点击"批量控制"按钮
- **界面介绍**：介绍批量控制面板的各个区域
  - 左侧：选中的设备列表
  - 右侧：批量操作选项

#### 2. 网络测试功能 (1分钟)
- **批量Ping**：演示批量ping功能
- **连接测试**：演示批量连接测试
- **结果查看**：展示测试结果和状态更新
- **实时反馈**：显示测试过程中的状态变化

#### 3. 分组管理功能 (1分钟)
- **移动到分组**：演示将设备移动到现有分组
- **分组选择**：展示分组下拉菜单的使用
- **创建新分组**：在批量控制面板中创建新分组
- **分组效果**：展示分组移动后的界面变化

#### 4. 设备管理功能 (30秒)
- **导出配置**：演示导出选中设备的配置
- **批量删除**：演示批量删除功能（注意安全确认）
- **操作确认**：展示危险操作的确认对话框

### 第四部分：高级功能展示 (1分钟)

#### 1. 操作历史记录 (30秒)
- **查看历史**：打开操作历史记录窗口
- **分组操作记录**：展示分组创建、移动等操作记录
- **撤销操作**：演示撤销分组操作
- **重做操作**：演示重做被撤销的操作

#### 2. 综合工作流 (30秒)
- **快速分组**：演示完整的设备分组工作流
- **批量测试**：对整个分组进行批量测试
- **状态监控**：展示分组设备的状态监控
- **效率提升**：总结分组管理带来的效率提升

## 🎯 演示要点

### 视觉效果重点
1. **选择状态的视觉反馈**：蓝色边框、选择框状态
2. **分组标识的清晰显示**：📁图标和分组名称
3. **批量操作的流畅性**：按钮状态变化、计数更新
4. **操作结果的即时反馈**：成功消息、状态更新

### 功能亮点强调
1. **操作简便性**：一键选择、快速分组
2. **批量处理能力**：同时操作多个设备
3. **安全性保障**：确认对话框、撤销功能
4. **专业性体现**：完整的设备管理流程

### 用户体验展示
1. **学习成本低**：直观的界面设计
2. **操作效率高**：批量操作节省时间
3. **管理便捷性**：灵活的分组管理
4. **错误恢复能力**：完善的撤销机制

## 📝 演示脚本台词

### 开场介绍
"接下来演示设备分组管理功能。这个功能让用户可以高效地管理大量设备，通过分组和批量操作大大提升工作效率。"

### 设备选择演示
"首先看设备选择功能。每个设备卡片都有选择框，可以单独选择，也可以使用全选按钮一次选择所有设备。选中的设备会有蓝色边框标识，批量控制按钮会显示当前选中的设备数量。"

### 分组创建演示
"选择设备后，可以点击创建分组按钮。比如我们把这些灯光设备归为'舞台灯光'分组。创建后，设备卡片会显示分组信息，左侧也会出现分组列表。"

### 批量控制演示
"批量控制面板是这个功能的核心。左侧显示选中的设备，右侧提供各种批量操作。可以进行网络测试、分组管理和设备管理。比如批量ping测试可以同时检测多个设备的连接状态。"

### 高级功能演示
"所有的分组操作都会记录在操作历史中，支持撤销和重做。这确保了操作的安全性，即使误操作也可以快速恢复。"

### 结束总结
"设备分组管理功能让设备管理变得更加高效和专业。无论是日常维护还是大型演出，都能显著提升工作效率。"

## 🔧 演示准备

### 测试数据准备
1. **创建测试设备**：准备8-10个测试设备
2. **设备类型多样化**：包含不同类型的设备
3. **状态模拟**：设置不同的在线/离线状态
4. **IP地址规划**：使用合理的IP地址范围

### 演示环境设置
1. **清空现有分组**：确保演示从空白状态开始
2. **重置选择状态**：清空所有设备选择
3. **清理历史记录**：可选择清空操作历史
4. **界面主题**：选择合适的主题进行演示

### 录制技巧
1. **鼠标高亮**：启用鼠标点击效果
2. **操作节奏**：适当的操作间隔，便于观众跟随
3. **界面缩放**：确保界面元素清晰可见
4. **音频质量**：清晰的解说音频

### 后期处理
1. **关键操作标注**：重要按钮的文字标注
2. **流程图解**：添加操作流程的图解说明
3. **功能对比**：展示使用前后的效率对比
4. **总结字幕**：功能特点的总结字幕

## 📊 演示数据示例

### 测试设备列表
```
舞台灯光组：
- LED灯条1 (*************) - 在线
- 摇头灯1 (*************) - 在线  
- 激光灯1 (*************) - 离线
- 烟雾机1 (*************) - 在线

音响系统组：
- 调音台1 (192.168.1.201) - 在线
- 功放1 (192.168.1.202) - 在线
- 音箱1 (192.168.1.203) - 在线
- 无线话筒 (192.168.1.204) - 异常

视频系统组：
- 投影仪1 (192.168.1.301) - 在线
- LED屏1 (192.168.1.302) - 在线
```

### 演示操作序列
1. 选择LED灯条1、摇头灯1、激光灯1、烟雾机1
2. 创建"舞台灯光"分组
3. 选择调音台1、功放1、音箱1、无线话筒
4. 创建"音响系统"分组
5. 批量测试舞台灯光组设备
6. 将投影仪1移动到新建的"视频系统"分组
7. 演示撤销和重做操作

---

*演示时长约8分钟，建议分段录制以确保质量。*
