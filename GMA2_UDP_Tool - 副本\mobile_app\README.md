# 🎭 多媒体演出控制中心 - 移动端APP

## 📱 支持平台

### 1. Windows平板直接运行版
- **文件**: `simple_app.py`
- **启动**: 双击 `启动平板控制器.bat`
- **优点**: 无需安装，直接运行
- **适用**: Windows平板、Windows电脑

### 2. Android APK版
- **文件**: `main.py` + `buildozer.spec`
- **构建**: 运行 `python build_app.py`
- **优点**: 原生Android应用
- **适用**: Android手机、Android平板

## 🚀 快速开始

### Windows平板使用方法

1. **准备工作**
   ```
   确保Windows平板已安装Python 3.7+
   如果没有，请从 https://www.python.org/downloads/ 下载安装
   ```

2. **启动应用**
   ```
   双击 "启动平板控制器.bat" 文件
   首次运行会自动安装必要的依赖包
   ```

3. **连接服务器**
   ```
   在电脑上启动演出控制系统
   点击"远程控制"按钮，启动服务器
   在平板APP中输入显示的服务器地址
   点击"连接"按钮
   ```

4. **开始控制**
   ```
   连接成功后即可远程控制演出系统
   播放场景、停止演出、唤醒设备等
   ```

### Android APK构建方法

1. **安装构建环境**
   ```bash
   pip install kivy buildozer
   ```

2. **构建APK**
   ```bash
   cd mobile_app
   python build_app.py
   选择 "1" 构建Android APK
   ```

3. **安装使用**
   ```
   将生成的APK文件传输到Android设备
   安装后即可使用
   ```

## 🎯 功能特性

### ✅ 已实现功能

- **🔗 服务器连接**: 自动发现和连接演出控制系统
- **📊 状态监控**: 实时显示系统状态和用户信息
- **🎬 场景控制**: 查看场景列表，一键播放任意场景
- **⏹ 停止控制**: 紧急停止当前播放的场景
- **🔌 设备唤醒**: 远程唤醒所有演出设备
- **🔄 自动刷新**: 定时更新系统状态和场景列表

### 📱 界面特点

- **简洁直观**: 大按钮设计，适合触屏操作
- **实时反馈**: 操作结果即时显示
- **状态清晰**: 连接状态、系统信息一目了然
- **响应迅速**: 本地界面，操作流畅

## 🌐 网络要求

- **同一局域网**: 平板和演出控制电脑需连接同一WiFi
- **端口开放**: 确保防火墙允许8080端口访问
- **网络稳定**: 建议使用5GHz WiFi以获得更好的响应速度

## 🔧 技术架构

### Windows版技术栈
- **界面框架**: tkinter (Python内置)
- **网络通信**: requests
- **多线程**: threading
- **优点**: 无需额外依赖，启动快速

### Android版技术栈
- **界面框架**: Kivy
- **网络通信**: requests
- **构建工具**: Buildozer
- **优点**: 原生APP体验

## 📋 使用场景

### 🎪 演出现场控制
- 导演在舞台任意位置控制演出进程
- 技术人员远程切换场景和灯光
- 紧急情况下快速停止演出

### 🏢 会议室控制
- 主持人使用平板控制多媒体播放
- 远程控制投影设备和音响系统
- 会议流程的灵活调整

### 🎓 教学应用
- 教师在教室任意位置控制课件
- 学生演示时的远程协助
- 多媒体教学设备的统一管理

## ⚠️ 注意事项

1. **网络安全**: 仅在可信的内网环境中使用
2. **设备兼容**: 建议使用Windows 10+或Android 7+
3. **性能要求**: 平板内存建议2GB以上
4. **电量管理**: 长时间使用建议连接充电器

## 🆘 故障排除

### 连接问题
```
问题: 无法连接服务器
解决: 
1. 检查网络连接
2. 确认服务器地址正确
3. 检查防火墙设置
4. 重启演出控制系统
```

### 性能问题
```
问题: 界面响应缓慢
解决:
1. 关闭其他应用程序
2. 检查网络信号强度
3. 重启平板设备
4. 使用有线网络连接
```

### 功能问题
```
问题: 某些功能无法使用
解决:
1. 检查用户权限
2. 更新到最新版本
3. 重新连接服务器
4. 联系技术支持
```

## 📞 技术支持

如有问题，请联系技术支持团队：
- 📧 邮箱: <EMAIL>
- 📱 电话: 400-123-4567
- 💬 QQ群: 123456789

## 🔄 版本更新

### v1.0 (当前版本)
- ✅ 基础远程控制功能
- ✅ Windows平板支持
- ✅ Android APK构建

### v1.1 (计划中)
- 🔄 iOS版本支持
- 🔄 更多控制功能
- 🔄 界面优化改进

---

**🎭 让演出控制更加便携和高效！**
