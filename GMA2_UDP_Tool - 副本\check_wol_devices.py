#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查网络唤醒设备配置
"""

import json
import os

def check_wol_devices():
    """检查WOL设备配置"""
    wol_file = "wol_devices.json"
    
    print("🔍 检查网络唤醒设备配置")
    print("=" * 40)
    
    if os.path.exists(wol_file):
        try:
            with open(wol_file, 'r', encoding='utf-8') as f:
                devices = json.load(f)
            
            print(f"✅ 找到配置文件: {wol_file}")
            print(f"📊 设备数量: {len(devices)}")
            print()
            
            if devices:
                print("📋 设备列表:")
                print("-" * 60)
                for i, device in enumerate(devices, 1):
                    print(f"{i}. 设备名称: {device.get('name', '未知')}")
                    print(f"   MAC地址: {device.get('mac', '未知')}")
                    print(f"   IP地址: {device.get('ip', '未知')}")
                    print(f"   描述: {device.get('description', '无')}")
                    print(f"   添加时间: {device.get('added_time', '未知')}")
                    print("-" * 60)
            else:
                print("📝 设备列表为空")
                
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
    else:
        print(f"❌ 配置文件不存在: {wol_file}")

def main():
    check_wol_devices()
    
    print("\n💡 使用说明:")
    print("1. 在演出控制系统中点击'网络唤醒'按钮")
    print("2. 点击'扫描网络'按钮开始扫描局域网")
    print("3. 等待扫描完成，查看发现的在线设备")
    print("4. 点击设备旁边的'添加'按钮")
    print("5. 设备会立即出现在左侧的设备列表中")
    print("6. 无需重新打开网络唤醒窗口")

if __name__ == '__main__':
    main()
