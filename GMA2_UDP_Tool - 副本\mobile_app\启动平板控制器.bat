@echo off
chcp 65001 >nul
title 演出控制中心 - 平板控制器

echo.
echo ========================================
echo    🎭 多媒体演出控制中心
echo    📱 平板控制器启动程序
echo ========================================
echo.

echo 正在启动平板控制器...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo.
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查requests模块
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装必要的依赖包...
    pip install requests
    if errorlevel 1 (
        echo ❌ 依赖包安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo 🚀 启动平板控制器...
echo.

REM 启动应用
python simple_app.py

if errorlevel 1 (
    echo.
    echo ❌ 应用启动失败
    pause
)

echo.
echo 👋 应用已关闭
pause
